/**
 * 重试机制管理器
 * 提供智能重试、错误处理和降级策略
 */

/**
 * 重试配置
 */
const RETRY_CONFIG = {
  // 不同服务的重试配置
  services: {
    umami: {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      timeout: 15000
    },
    firebase: {
      maxRetries: 2,
      baseDelay: 500,
      maxDelay: 5000,
      backoffFactor: 1.5,
      timeout: 10000
    },
    vercel: {
      maxRetries: 2,
      baseDelay: 1000,
      maxDelay: 8000,
      backoffFactor: 2,
      timeout: 12000
    }
  },
  
  // 默认配置
  default: {
    maxRetries: 2,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffFactor: 1.5,
    timeout: 10000
  }
}

/**
 * 错误类型分类
 */
const ERROR_TYPES = {
  NETWORK: 'network',
  TIMEOUT: 'timeout',
  AUTH: 'auth',
  RATE_LIMIT: 'rate_limit',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown'
}

/**
 * 分析错误类型
 * @param {Error} error 错误对象
 * @returns {string} 错误类型
 */
function analyzeError(error) {
  if (!error) return ERROR_TYPES.UNKNOWN
  
  const message = error.message?.toLowerCase() || ''
  const status = error.status || error.statusCode
  
  // 网络错误
  if (message.includes('network') || message.includes('fetch') || 
      message.includes('connection') || error.code === 'ECONNREFUSED') {
    return ERROR_TYPES.NETWORK
  }
  
  // 超时错误
  if (message.includes('timeout') || error.code === 'ETIMEDOUT') {
    return ERROR_TYPES.TIMEOUT
  }
  
  // 认证错误
  if (status === 401 || status === 403 || message.includes('unauthorized')) {
    return ERROR_TYPES.AUTH
  }
  
  // 限流错误
  if (status === 429 || message.includes('rate limit')) {
    return ERROR_TYPES.RATE_LIMIT
  }
  
  // 服务器错误
  if (status >= 500 && status < 600) {
    return ERROR_TYPES.SERVER
  }
  
  // 客户端错误
  if (status >= 400 && status < 500) {
    return ERROR_TYPES.CLIENT
  }
  
  return ERROR_TYPES.UNKNOWN
}

/**
 * 判断错误是否可重试
 * @param {string} errorType 错误类型
 * @param {number} attempt 当前尝试次数
 * @returns {boolean}
 */
function isRetryableError(errorType, attempt) {
  // 认证错误和客户端错误通常不可重试
  if (errorType === ERROR_TYPES.AUTH || errorType === ERROR_TYPES.CLIENT) {
    return false
  }
  
  // 限流错误可以重试，但需要更长的延迟
  return true
}

/**
 * 计算重试延迟
 * @param {number} attempt 尝试次数 (从0开始)
 * @param {Object} config 重试配置
 * @param {string} errorType 错误类型
 * @returns {number} 延迟时间(毫秒)
 */
function calculateDelay(attempt, config, errorType) {
  let delay = config.baseDelay * Math.pow(config.backoffFactor, attempt)
  
  // 限流错误使用更长的延迟
  if (errorType === ERROR_TYPES.RATE_LIMIT) {
    delay *= 3
  }
  
  // 添加随机抖动 (±25%)
  const jitter = delay * 0.25 * (Math.random() * 2 - 1)
  delay += jitter
  
  return Math.min(delay, config.maxDelay)
}

/**
 * 重试管理器类
 */
export class RetryManager {
  constructor() {
    this.activeRetries = new Map()
    this.errorStats = new Map()
  }

  /**
   * 执行带重试的异步操作
   * @param {Function} operation 要执行的操作
   * @param {string} service 服务名称
   * @param {Object} options 选项
   * @returns {Promise} 操作结果
   */
  async executeWithRetry(operation, service = 'default', options = {}) {
    const config = RETRY_CONFIG.services[service] || RETRY_CONFIG.default
    const operationId = options.operationId || `${service}_${Date.now()}`
    
    // 防止重复执行相同操作
    if (this.activeRetries.has(operationId)) {
      console.warn(`操作 ${operationId} 已在执行中`)
      return this.activeRetries.get(operationId)
    }

    const retryPromise = this._executeWithRetryInternal(
      operation, 
      config, 
      service, 
      operationId,
      options
    )
    
    this.activeRetries.set(operationId, retryPromise)
    
    try {
      const result = await retryPromise
      return result
    } finally {
      this.activeRetries.delete(operationId)
    }
  }

  /**
   * 内部重试执行逻辑
   */
  async _executeWithRetryInternal(operation, config, service, operationId, options) {
    let lastError = null
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        console.log(`执行操作 ${operationId}, 尝试 ${attempt + 1}/${config.maxRetries + 1}`)
        
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Operation timeout')), config.timeout)
        })
        
        const result = await Promise.race([
          operation(),
          timeoutPromise
        ])
        
        // 成功时重置错误统计
        this.resetErrorStats(service)
        console.log(`操作 ${operationId} 成功完成`)
        
        return result
        
      } catch (error) {
        lastError = error
        const errorType = analyzeError(error)
        
        // 记录错误统计
        this.recordError(service, errorType)
        
        console.warn(`操作 ${operationId} 失败 (尝试 ${attempt + 1}):`, {
          error: error.message,
          type: errorType,
          status: error.status || error.statusCode
        })
        
        // 检查是否应该重试
        if (attempt >= config.maxRetries || !isRetryableError(errorType, attempt)) {
          console.error(`操作 ${operationId} 最终失败，不再重试`)
          break
        }
        
        // 计算延迟并等待
        const delay = calculateDelay(attempt, config, errorType)
        console.log(`等待 ${delay}ms 后重试...`)
        await this.sleep(delay)
      }
    }
    
    // 所有重试都失败了
    throw new RetryError(
      `操作失败，已重试 ${config.maxRetries} 次`,
      lastError,
      service,
      this.getErrorStats(service)
    )
  }

  /**
   * 记录错误统计
   */
  recordError(service, errorType) {
    if (!this.errorStats.has(service)) {
      this.errorStats.set(service, {
        total: 0,
        types: {},
        lastError: null,
        firstErrorTime: Date.now()
      })
    }
    
    const stats = this.errorStats.get(service)
    stats.total++
    stats.types[errorType] = (stats.types[errorType] || 0) + 1
    stats.lastError = Date.now()
  }

  /**
   * 重置错误统计
   */
  resetErrorStats(service) {
    this.errorStats.delete(service)
  }

  /**
   * 获取错误统计
   */
  getErrorStats(service) {
    return this.errorStats.get(service) || null
  }

  /**
   * 获取所有服务的健康状态
   */
  getHealthStatus() {
    const status = {}
    
    for (const service of Object.keys(RETRY_CONFIG.services)) {
      const stats = this.getErrorStats(service)
      
      if (!stats) {
        status[service] = { health: 'healthy', errors: 0 }
      } else {
        const recentErrors = stats.total
        const timeSinceFirstError = Date.now() - stats.firstErrorTime
        
        let health = 'healthy'
        if (recentErrors > 5 && timeSinceFirstError < 5 * 60 * 1000) {
          health = 'degraded'
        }
        if (recentErrors > 10 && timeSinceFirstError < 10 * 60 * 1000) {
          health = 'unhealthy'
        }
        
        status[service] = {
          health,
          errors: recentErrors,
          errorTypes: stats.types,
          lastError: stats.lastError
        }
      }
    }
    
    return status
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.activeRetries.clear()
    this.errorStats.clear()
  }
}

/**
 * 自定义重试错误类
 */
export class RetryError extends Error {
  constructor(message, originalError, service, errorStats) {
    super(message)
    this.name = 'RetryError'
    this.originalError = originalError
    this.service = service
    this.errorStats = errorStats
  }
}

// 创建全局重试管理器实例
export const retryManager = new RetryManager()

/**
 * 重试装饰器函数
 * @param {string} service 服务名称
 * @param {Object} options 选项
 */
export function withRetry(service = 'default', options = {}) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value
    
    descriptor.value = async function(...args) {
      const operationId = `${propertyName}_${JSON.stringify(args).slice(0, 50)}`
      
      return retryManager.executeWithRetry(
        () => method.apply(this, args),
        service,
        { ...options, operationId }
      )
    }
    
    return descriptor
  }
}
