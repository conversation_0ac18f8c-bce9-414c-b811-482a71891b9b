/* eslint-disable @next/next/no-sync-scripts */
import BLOG from '@/blog.config'
import Document, { Head, Html, Main, NextScript } from 'next/document'

class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const initialProps = await Document.getInitialProps(ctx)
    return { ...initialProps }
  }

  render() {
    return (
      <Html lang={BLOG.LANG}>
        <Head>
          {/* 控制台日志清理器 - 必须在所有其他脚本之前加载 */}
          <script src="/js/console-cleaner.js" />

          {/* 预加载字体 (你原来就有的) */}
          {BLOG.FONT_AWESOME && (
            <>
              <link
                rel='preload'
                href={BLOG.FONT_AWESOME}
                as='style'
                crossOrigin='anonymous'
              />
              <link
                rel='stylesheet'
                href={BLOG.FONT_AWESOME}
                crossOrigin='anonymous'
                referrerPolicy='no-referrer'
              />
            </>
          )}

          {/* ================== 这是新的优化代码 START ================== */}

          {/* 1. 提前和 Live2D 模型服务器建立连接 */}
          <link rel='preconnect' href='https://live2d.wobshare.us.kg' />
          
          {/* 2. 提示浏览器提前下载 Live2D 核心脚本，'as=script' 表示它是个脚本 */}
          <link
            rel='preload'
            href='/js/live2d.min.js'
            as='script'
          />
          
          {/* ================== 新的优化代码 END ==================== */}

          {/* Umami Analytics 统计脚本 */}
          <script
            defer
            src="https://cloud.umami.is/script.js"
            data-website-id="8e485e40-2d2b-44b2-b792-7d00bbc235dd"
          />
        </Head>

        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}

export default MyDocument
