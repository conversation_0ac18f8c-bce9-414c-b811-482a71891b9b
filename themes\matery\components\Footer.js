import { BeiAnGongAn } from '@/components/BeiAnGongAn'
import { siteConfig } from '@/lib/config'
import VisitorStats from './VisitorStats'

const Footer = ({ title }) => {
  const d = new Date()
  const currentYear = d.getFullYear()
  const copyrightDate = (function () {
    if (
      Number.isInteger(siteConfig('SINCE')) &&
      siteConfig('SINCE') < currentYear
    ) {
      return siteConfig('SINCE') + '-' + currentYear
    }
    return currentYear
  })()

  return (
    <footer className='relative z-10 dark:bg-black flex-shrink-0 bg-indigo-700 text-gray-300 justify-center text-center m-auto w-full leading-6  dark:text-gray-100 text-sm p-6'>
      {/* <DarkModeButton/> */}
      <i className='fas fa-copyright' /> {`${copyrightDate}`}{' '}
      <span>
        <span className='w-5 mx-1 text-center'>
          <i className=' animate-pulse fas fa-heart' />
        </span>{' '}
        <a
          href={siteConfig('LINK')}
          className='underline font-bold  dark:text-gray-300 '>
          {siteConfig('AUTHOR')}
        </a>
        .<br />
        {siteConfig('BEI_AN') && (
          <>
            <i className='fas fa-shield-alt' />{' '}
            <a href='https://beian.miit.gov.cn/' className='mr-2'>
              {siteConfig('BEI_AN')}
            </a>
            <br />
          </>
        )}
        <BeiAnGongAn />
        <div className='mt-2'>
          <div className='mb-1'>
            <VisitorStats />
          </div>
          <div className='flex items-center justify-center gap-1 text-sm mb-1'>
            <span className='busuanzi_container_site_pv flex items-center gap-1'>
              <i className='fas fa-eye'></i>
              <span className='busuanzi_value_site_pv'></span>
            </span>
            <span className='busuanzi_container_site_uv flex items-center gap-1'>
              <i className='fas fa-users'></i>
              <span className='busuanzi_value_site_uv'></span>
            </span>
          </div>
          <div>
            Powered by{' '}
            <a
              href='https://wobshare.us.kg/reward'
              className='underline font-bold  dark:text-gray-300 '>
              𝓌𝑜𝒷
            </a>
          </div>
        </div>
      </span>
      <br />
    </footer>
  )
}

export default Footer
