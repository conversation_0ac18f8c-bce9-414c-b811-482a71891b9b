/**
 * Firebase 测试 API 端点
 * 测试 Firebase Realtime Database 连接和功能
 */

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 动态导入 Firebase 模块
    const { onlineUserManager } = await import('@/lib/firebase')
    
    console.log('测试 Firebase 连接...')
    
    // 测试获取今日访客数和在线用户数
    const [todayVisitors, onlineUsers] = await Promise.all([
      onlineUserManager.getTodayVisitors(),
      onlineUserManager.getOnlineCount()
    ])
    
    console.log('Firebase 测试结果:', { todayVisitors, onlineUsers })
    
    const result = {
      success: true,
      todayVisitors: todayVisitors || 0,
      onlineUsers: onlineUsers || 1,
      source: 'firebase',
      timestamp: new Date().toISOString()
    }
    
    res.status(200).json(result)
  } catch (error) {
    console.error('Firebase 测试错误:', error)
    res.status(500).json({ 
      success: false,
      error: 'Firebase connection failed',
      message: error.message,
      todayVisitors: 0,
      onlineUsers: 1 
    })
  }
}
