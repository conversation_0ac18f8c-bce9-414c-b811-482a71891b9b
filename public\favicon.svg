<svg fill="none" viewBox="15 10 100 73" width="100" height="73" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* 渐变动画 */
    @keyframes gradientAnimation {
      0% { stop-color: #05DFD7; }
      50% { stop-color: #6A82FB; }
      100% { stop-color: #FC5C7D; }
    }

    /* 设置渐变的动画效果 */
    .animated-gradient stop {
      animation: gradientAnimation 5s infinite alternate;
    }
  </style>

  <!-- 定义渐变颜色 -->
  <defs>
    <linearGradient id="dynamicGradient" x1="0%" y1="0%" x2="100%" y2="100%" class="animated-gradient">
      <stop offset="0%" stop-color="#05DFD7" />
      <stop offset="50%" stop-color="#6A82FB" />
      <stop offset="100%" stop-color="#FC5C7D" />
    </linearGradient>
  </defs>

  <!-- 外圆 -->
  <circle cx="60" cy="42" r="28" stroke="url(#dynamicGradient)" stroke-width="3" fill="none" />

  <!-- 无限符号（y方向继续增加） -->
  <path 
    d="M35,42 C35,28 60,28 60,42 C60,56 85,56 85,42 C85,28 60,28 60,42 C60,56 35,56 35,42 Z" 
    stroke="url(#dynamicGradient)" 
    stroke-width="2" 
    fill="none" />
</svg>
