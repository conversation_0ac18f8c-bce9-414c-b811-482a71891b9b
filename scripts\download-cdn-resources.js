/**
 * CDN资源本地化下载脚本
 * 下载项目中使用的CDN资源到本地，实现本地优先、CDN备用的策略
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 创建目录（如果不存在）
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dirPath}`);
  }
}

// 下载文件
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    console.log(`📥 下载: ${url}`);
    
    const file = fs.createWriteStream(outputPath);
    
    protocol.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          console.log(`✅ 下载完成: ${outputPath}`);
          resolve();
        });
      } else if (response.statusCode === 301 || response.statusCode === 302) {
        // 处理重定向
        const redirectUrl = response.headers.location;
        console.log(`🔄 重定向到: ${redirectUrl}`);
        downloadFile(redirectUrl, outputPath).then(resolve).catch(reject);
      } else {
        reject(new Error(`HTTP ${response.statusCode}: ${url}`));
      }
    }).on('error', (err) => {
      fs.unlink(outputPath, () => {}); // 删除部分下载的文件
      reject(err);
    });
  });
}

// 需要下载的资源列表
const resources = [
  // APlayer 音乐播放器
  {
    url: 'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js',
    path: 'public/js/aplayer.min.js'
  },
  {
    url: 'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css',
    path: 'public/css/aplayer.min.css'
  },
  
  // MetingJS
  {
    url: 'https://unpkg.com/meting@2.0.1/dist/Meting.min.js',
    path: 'public/js/meting.min.js'
  },
  
  // Animate.css
  {
    url: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
    path: 'public/css/animate.min.css'
  },
  
  // Mermaid 图表
  {
    url: 'https://cdnjs.cloudflare.com/ajax/libs/mermaid/11.4.0/mermaid.min.js',
    path: 'public/js/mermaid.min.js'
  },
  
  // VConsole (调试工具)
  {
    url: 'https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js',
    path: 'public/js/vconsole.min.js'
  },
  
  // Gitalk 评论系统
  {
    url: 'https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.min.js',
    path: 'public/js/gitalk.min.js'
  },
  {
    url: 'https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.css',
    path: 'public/css/gitalk.css'
  },
  
  // Valine 评论系统
  {
    url: 'https://unpkg.com/valine@1.5.1/dist/Valine.min.js',
    path: 'public/js/valine.min.js'
  },
  
  // WebWhiz AI组件
  {
    url: 'https://www.unpkg.com/webwhiz@1.0.0/dist/sdk.js',
    path: 'public/js/webwhiz-sdk.js'
  }
];

// 主下载函数
async function downloadResources() {
  console.log('🚀 开始下载CDN资源到本地...\n');
  
  // 确保目录存在
  ensureDir('public/js');
  ensureDir('public/css');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const resource of resources) {
    try {
      // 检查文件是否已存在
      if (fs.existsSync(resource.path)) {
        console.log(`⏭️  跳过已存在的文件: ${resource.path}`);
        continue;
      }
      
      await downloadFile(resource.url, resource.path);
      successCount++;
    } catch (error) {
      console.error(`❌ 下载失败: ${resource.url}`);
      console.error(`   错误: ${error.message}`);
      failCount++;
    }
  }
  
  console.log('\n📊 下载统计:');
  console.log(`✅ 成功: ${successCount}`);
  console.log(`❌ 失败: ${failCount}`);
  console.log(`📁 总计: ${resources.length}`);
  
  if (failCount > 0) {
    console.log('\n⚠️  部分资源下载失败，但不影响使用，系统会自动回退到CDN');
  } else {
    console.log('\n🎉 所有资源下载完成！');
  }
}

// 运行下载
if (require.main === module) {
  downloadResources().catch(console.error);
}

module.exports = { downloadResources, downloadFile, ensureDir };
