/**
 * 实时在线用户追踪器
 * 提供真实的在线用户检测功能
 */

import { onlineUserManager } from './firebase'

class RealTimeUserTracker {
  constructor() {
    this.isActive = false
    this.userId = null
    this.heartbeatInterval = null
    this.visibilityChangeHandler = null
    this.beforeUnloadHandler = null
    this.onlineUsers = 1
    this.callbacks = new Set()
  }

  /**
   * 初始化实时用户追踪
   */
  async init() {
    if (this.isActive) return

    try {
      // 生成用户ID
      this.userId = this.generateUserId()
      
      // 添加到Firebase在线用户列表
      await onlineUserManager.addUser(this.userId)
      await onlineUserManager.addTodayVisitor(this.userId)
      
      // 启动心跳
      this.startHeartbeat()
      
      // 监听页面可见性变化
      this.setupVisibilityTracking()
      
      // 监听页面关闭
      this.setupUnloadTracking()
      
      // 获取初始在线用户数
      await this.updateOnlineCount()
      
      this.isActive = true
      console.log('实时用户追踪已启动:', this.userId)
      
    } catch (error) {
      console.error('实时用户追踪初始化失败:', error)
    }
  }

  /**
   * 停止实时用户追踪
   */
  async stop() {
    if (!this.isActive) return

    try {
      // 停止心跳
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }

      // 移除事件监听器
      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      }
      if (this.beforeUnloadHandler) {
        window.removeEventListener('beforeunload', this.beforeUnloadHandler)
      }

      // 从Firebase移除用户
      if (this.userId) {
        await onlineUserManager.removeUser(this.userId)
      }

      this.isActive = false
      console.log('实时用户追踪已停止')
      
    } catch (error) {
      console.error('停止实时用户追踪失败:', error)
    }
  }

  /**
   * 启动心跳机制
   */
  startHeartbeat() {
    // 每30秒发送一次心跳
    this.heartbeatInterval = setInterval(async () => {
      try {
        if (this.userId && !document.hidden) {
          await onlineUserManager.updateHeartbeat(this.userId)
          await this.updateOnlineCount()
        }
      } catch (error) {
        console.warn('心跳更新失败:', error)
      }
    }, 30000)
  }

  /**
   * 设置页面可见性追踪
   */
  setupVisibilityTracking() {
    this.visibilityChangeHandler = async () => {
      if (document.hidden) {
        // 页面隐藏时暂停心跳
        console.log('页面隐藏，暂停心跳')
      } else {
        // 页面显示时恢复心跳并更新状态
        console.log('页面显示，恢复心跳')
        if (this.userId) {
          await onlineUserManager.updateHeartbeat(this.userId)
          await this.updateOnlineCount()
        }
      }
    }
    
    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
  }

  /**
   * 设置页面卸载追踪
   */
  setupUnloadTracking() {
    this.beforeUnloadHandler = () => {
      // 页面关闭时立即移除用户（使用sendBeacon确保发送）
      if (this.userId && navigator.sendBeacon) {
        const data = JSON.stringify({ userId: this.userId, action: 'remove' })
        navigator.sendBeacon('/api/remove-online-user', data)
      }
    }
    
    window.addEventListener('beforeunload', this.beforeUnloadHandler)
  }

  /**
   * 更新在线用户数
   */
  async updateOnlineCount() {
    try {
      const count = await onlineUserManager.getOnlineCount()
      if (count !== this.onlineUsers) {
        this.onlineUsers = count
        this.notifyCallbacks(count)
      }
    } catch (error) {
      console.warn('获取在线用户数失败:', error)
    }
  }

  /**
   * 生成用户ID
   */
  generateUserId() {
    // 尝试从localStorage获取已有ID
    let userId = localStorage.getItem('realtime_user_id')
    if (!userId) {
      userId = 'rt_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
      localStorage.setItem('realtime_user_id', userId)
    }
    return userId
  }

  /**
   * 获取当前在线用户数
   */
  getOnlineUsers() {
    return this.onlineUsers
  }

  /**
   * 订阅在线用户数变化
   */
  subscribe(callback) {
    this.callbacks.add(callback)
    return () => this.callbacks.delete(callback)
  }

  /**
   * 通知所有订阅者
   */
  notifyCallbacks(count) {
    this.callbacks.forEach(callback => {
      try {
        callback(count)
      } catch (error) {
        console.error('回调函数执行失败:', error)
      }
    })
  }

  /**
   * 获取用户活动状态
   */
  getActivityStatus() {
    return {
      isActive: this.isActive,
      userId: this.userId,
      onlineUsers: this.onlineUsers,
      isVisible: !document.hidden,
      lastHeartbeat: new Date().toISOString()
    }
  }
}

// 创建全局实例
export const realTimeUserTracker = new RealTimeUserTracker()

/**
 * React Hook for real-time user tracking
 */
export function useRealTimeUsers() {
  const [onlineUsers, setOnlineUsers] = useState(1)
  const [isTracking, setIsTracking] = useState(false)

  useEffect(() => {
    let unsubscribe = null

    const initTracking = async () => {
      try {
        await realTimeUserTracker.init()
        setIsTracking(true)
        setOnlineUsers(realTimeUserTracker.getOnlineUsers())
        
        // 订阅在线用户数变化
        unsubscribe = realTimeUserTracker.subscribe((count) => {
          setOnlineUsers(count)
        })
      } catch (error) {
        console.error('实时用户追踪初始化失败:', error)
        setIsTracking(false)
      }
    }

    initTracking()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
      realTimeUserTracker.stop()
      setIsTracking(false)
    }
  }, [])

  return {
    onlineUsers,
    isTracking,
    activityStatus: realTimeUserTracker.getActivityStatus()
  }
}

export default realTimeUserTracker
