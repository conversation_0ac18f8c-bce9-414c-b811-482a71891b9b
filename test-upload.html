<!DOCTYPE html>
<html>
<head>
    <title>File Upload Test</title>
</head>
<body>
    <h1>File Upload Test</h1>
    <input type="file" id="fileInput" />
    <button onclick="uploadFile()">Upload</button>
    <div id="result"></div>

    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }

            console.log('Selected file:', file);
            console.log('File name:', file.name);
            console.log('File type:', file.type);
            console.log('File size:', file.size);

            const formData = new FormData();
            formData.append('file', file);

            console.log('FormData entries:', [...formData.entries()]);

            try {
                const response = await fetch('https://wobchatai.wobys.dpdns.org/api/file/upload', {
                    method: 'POST',
                    headers: {
                        'X-Chat-Session': 'test-session-' + Date.now()
                    },
                    body: formData
                });

                const result = await response.json();
                console.log('Upload result:', result);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('Upload error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
