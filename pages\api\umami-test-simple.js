/**
 * 简单的 Umami API 测试端点
 * 测试基本的 API 连接和认证
 */

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const websiteId = '8e485e40-2d2b-44b2-b792-7d00bbc235dd'
  const apiKey = 'api_J5rJQDJgh3a4a57ni3uKCZbgHFCAD6s6'

  const tests = []

  // 测试 1: 测试基本连接
  try {
    console.log('测试 1: 基本连接测试')
    const response = await fetch('https://cloud.umami.is/api/heartbeat', {
      method: 'GET'
    })

    tests.push({
      name: 'Basic Connection',
      url: 'https://cloud.umami.is/api/heartbeat',
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.text() : await response.text()
    })
  } catch (error) {
    tests.push({
      name: 'Basic Connection',
      success: false,
      error: error.message
    })
  }

  // 测试 2: 获取网站基本信息（使用正确的 API 端点）
  try {
    console.log('测试 2: 获取网站信息')
    const response = await fetch(`https://api.umami.is/v1/websites/${websiteId}`, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    tests.push({
      name: 'Website Info (v1 API)',
      url: `https://api.umami.is/v1/websites/${websiteId}`,
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text()
    })
  } catch (error) {
    tests.push({
      name: 'Website Info (v1 API)',
      success: false,
      error: error.message
    })
  }

  // 测试 3: 获取统计数据（无时间参数）
  try {
    console.log('测试 3: 获取统计数据（无参数）')
    const response = await fetch(`https://api.umami.is/v1/websites/${websiteId}/stats`, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    tests.push({
      name: 'Stats (No Params) v1',
      url: `https://api.umami.is/v1/websites/${websiteId}/stats`,
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text()
    })
  } catch (error) {
    tests.push({
      name: 'Stats (No Params) v1',
      success: false,
      error: error.message
    })
  }

  // 测试 4: 获取今日统计数据
  try {
    console.log('测试 3: 获取今日统计数据')
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000)

    const params = new URLSearchParams({
      startAt: startOfDay.getTime().toString(),
      endAt: endOfDay.getTime().toString()
    })

    const url = `https://api.umami.is/v1/websites/${websiteId}/stats?${params}`
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    tests.push({
      name: 'Today Stats',
      url: url,
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text(),
      timeParams: {
        startAt: startOfDay.toISOString(),
        endAt: endOfDay.toISOString()
      }
    })
  } catch (error) {
    tests.push({
      name: 'Today Stats',
      success: false,
      error: error.message
    })
  }

  // 返回测试结果
  res.status(200).json({
    websiteId,
    apiKeyPrefix: apiKey.substring(0, 15) + '...',
    timestamp: new Date().toISOString(),
    tests,
    summary: {
      total: tests.length,
      passed: tests.filter(t => t.success).length,
      failed: tests.filter(t => !t.success).length
    }
  })
}
