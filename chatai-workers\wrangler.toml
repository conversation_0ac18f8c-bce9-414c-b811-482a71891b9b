name = "chatai-workers"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 默认环境配置
[vars]
GEMINI_API_KEYS = "AIzaSyAXvLdyZNtnbIETj0_LxQhOzlnXkDnb570,AIzaSyDj-sgg4UDzohVSaeEqiXNrjQ05wBHtKBc"

# KV 存储 (用于聊天数据)
[[kv_namespaces]]
binding = "CHAT_STORAGE"
id = "6d6792bc55fb4e2a885d27f7ef1d6e74"
preview_id = "6d6792bc55fb4e2a885d27f7ef1d6e74"

# R2 存储 (用于文件上传)
[[r2_buckets]]
binding = "FILE_STORAGE"
bucket_name = "r2-wobchatai"
preview_bucket_name = "r2-wobchatai"

# Durable Objects (用于实时聊天)
[[durable_objects.bindings]]
name = "CHAT_ROOM"
class_name = "ChatRoom"

[[migrations]]
tag = "v1"
new_sqlite_classes = ["ChatRoom"]

# 路由配置
[triggers]
crons = ["0 0 * * *"]

# 资源限制
# [limits]
# cpu_ms = 50000

# 生产环境配置
[env.production]
name = "chatai-workers-production"

[env.production.vars]
ENVIRONMENT = "production"

[[env.production.kv_namespaces]]
binding = "CHAT_STORAGE"
id = "6d6792bc55fb4e2a885d27f7ef1d6e74"
preview_id = "6d6792bc55fb4e2a885d27f7ef1d6e74"

[[env.production.r2_buckets]]
binding = "FILE_STORAGE"
bucket_name = "r2-wobchatai"
preview_bucket_name = "r2-wobchatai"

[[env.production.durable_objects.bindings]]
name = "CHAT_ROOM"
class_name = "ChatRoom"
