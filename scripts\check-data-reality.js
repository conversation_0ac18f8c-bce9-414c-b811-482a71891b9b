#!/usr/bin/env node

/**
 * 数据真实性检查脚本
 * 检查当前统计数据的真实性和配置状态
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.SITE_URL || 'http://localhost:3000';

async function checkDataReality() {
  console.log('🔍 检查访问统计数据的真实性...\n');

  // 1. 检查 Umami 配置
  console.log('📊 Umami Analytics 检查:');
  try {
    const umamiResponse = await fetch(`${BASE_URL}/api/umami-debug`);
    const umamiDebug = await umamiResponse.json();
    
    console.log(`  配置状态:`);
    console.log(`    Website ID: ${umamiDebug.environment.hasWebsiteId ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`    API Key: ${umamiDebug.environment.hasApiKey ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`    Website ID: ${umamiDebug.environment.websiteId}`);
    console.log(`    API Key前缀: ${umamiDebug.environment.apiKeyPrefix}`);
    
    // 检查连接测试结果
    const connectionTest = umamiDebug.tests.find(t => t.name === 'Basic Connection (v1 API)');
    const authTest = umamiDebug.tests.find(t => t.name === 'Auth: X-Umami-API-Key (Correct)');
    
    console.log(`  连接测试:`);
    console.log(`    基础连接: ${connectionTest?.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`    API认证: ${authTest?.success ? '✅ 成功' : '❌ 失败'}`);
    
    if (connectionTest?.success && authTest?.success) {
      console.log(`  🎉 Umami 数据是真实的！`);
    } else {
      console.log(`  ⚠️  Umami 连接有问题，可能使用模拟数据`);
    }
  } catch (error) {
    console.log(`  ❌ Umami 检查失败: ${error.message}`);
  }

  console.log('\n🔥 Firebase 检查:');
  try {
    const firebaseResponse = await fetch(`${BASE_URL}/api/firebase-test`);
    const firebaseData = await firebaseResponse.json();
    
    if (firebaseData.success) {
      console.log(`  连接状态: ✅ 成功`);
      console.log(`  今日访客: ${firebaseData.todayVisitors}`);
      console.log(`  在线用户: ${firebaseData.onlineUsers}`);
      console.log(`  🎉 Firebase 数据是真实的！`);
    } else {
      console.log(`  连接状态: ❌ 失败`);
      console.log(`  错误信息: ${firebaseData.message}`);
      console.log(`  ⚠️  Firebase 连接有问题，可能使用模拟数据`);
    }
  } catch (error) {
    console.log(`  ❌ Firebase 检查失败: ${error.message}`);
  }

  console.log('\n▲ Vercel Analytics 检查:');
  try {
    const vercelResponse = await fetch(`${BASE_URL}/api/vercel-stats`);
    const vercelData = await vercelResponse.json();
    
    if (vercelData.success) {
      console.log(`  连接状态: ✅ 成功`);
      console.log(`  数据源: ${vercelData.source}`);
      console.log(`  今日访客: ${vercelData.todayVisitors}`);
      console.log(`  🎉 Vercel 数据是真实的！`);
    } else {
      console.log(`  连接状态: ❌ 失败`);
      console.log(`  错误信息: ${vercelData.error || vercelData.message}`);
      console.log(`  ⚠️  Vercel 连接有问题，可能使用模拟数据`);
    }
  } catch (error) {
    console.log(`  ❌ Vercel 检查失败: ${error.message}`);
  }

  // 2. 检查当前显示的数据
  console.log('\n📈 当前统计数据:');
  try {
    // 模拟获取前端显示的数据
    const response = await fetch(`${BASE_URL}/api/umami-stats`);
    const data = await response.json();
    
    if (data.success) {
      console.log(`  今日访客: ${data.todayVisitors}`);
      console.log(`  在线用户: ${data.onlineUsers}`);
      console.log(`  数据源: ${data.source}`);
      console.log(`  更新时间: ${data.timestamp}`);
    }
  } catch (error) {
    console.log(`  ❌ 获取统计数据失败: ${error.message}`);
  }

  // 3. 数据真实性评估
  console.log('\n🎯 数据真实性评估:');
  console.log('  基于以上检查结果，你的访问统计数据真实性如下：');
  console.log('  - 如果 Umami/Firebase/Vercel 中任何一个显示 ✅ 成功，那么数据是真实的');
  console.log('  - 如果所有服务都显示 ❌ 失败，那么使用的是本地模拟数据');
  console.log('  - 本地模拟数据会根据访问情况生成基础的统计信息');

  // 4. Vercel 部署建议
  console.log('\n🚀 Vercel 部署注意事项:');
  console.log('  1. 环境变量配置:');
  console.log('     - 需要在 Vercel 项目设置中配置所有必要的环境变量');
  console.log('     - 包括 UMAMI_WEBSITE_ID, UMAMI_API_KEY 等');
  console.log('  2. API 密钥安全:');
  console.log('     - 生产环境中应使用真实的 API 密钥');
  console.log('     - 避免在代码中硬编码敏感信息');
  console.log('  3. 数据源优先级:');
  console.log('     - Firebase > Umami > Vercel > 本地模拟');
  console.log('     - 确保至少有一个数据源正常工作');
}

// 运行检查
if (require.main === module) {
  checkDataReality().catch(console.error);
}

module.exports = { checkDataReality };
