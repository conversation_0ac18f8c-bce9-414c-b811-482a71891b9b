/**
 * Umami Analytics API 代理端点
 * 解决 CORS 问题，通过服务器端调用 Umami API
 *
 * 配置信息:
 * - Website ID: 8e485e40-2d2b-44b2-b792-7d00bbc235dd
 * - API Key: api_J5rJQDJgh3a4a57ni3uKCZbgHFCAD6s6
 * - Domain: wobshare.us.kg
 */

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const websiteId = '8e485e40-2d2b-44b2-b792-7d00bbc235dd'
    const apiKey = 'api_J5rJQDJgh3a4a57ni3uKCZbgHFCAD6s6'



    // 获取今日时间范围 (UTC)
    const now = new Date()
    // 使用UTC时间确保时区一致性
    const today = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()))
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

    // 转换为毫秒时间戳
    const startAt = today.getTime()
    const endAt = tomorrow.getTime()



    // 构建 API URL
    const params = new URLSearchParams({
      startAt: startAt.toString(),
      endAt: endAt.toString()
    })

    // 使用正确的 Umami Cloud API 端点
    const apiUrl = `https://api.umami.is/v1/websites/${websiteId}/stats?${params}`


    // 调用 Umami Cloud API（使用正确的认证头）
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'WobShare-Analytics/1.0'
      }
    })



    if (!response.ok) {
      const errorText = await response.text()
      console.error('Umami API 调用失败:')
      console.error('- 状态码:', response.status)
      console.error('- 状态文本:', response.statusText)
      console.error('- 错误详情:', errorText)
      console.error('- API URL:', apiUrl)

      // 如果是 401 错误，提供详细的调试信息
      if (response.status === 401) {
        console.error('认证失败分析:')
        console.error('- 检查 API 密钥是否正确:', apiKey)
        console.error('- 检查网站 ID 是否正确:', websiteId)
        console.error('- 检查 API 端点是否正确')
        console.error('- 建议：登录 Umami Cloud 检查 API 密钥状态')
      }

      return res.status(response.status).json({
        error: `Umami API error: ${response.status}`,
        details: errorText,
        success: false,
        debug: {
          websiteId,
          apiKeyPrefix: apiKey.substring(0, 15),
          timestamp: new Date().toISOString(),
          apiUrl,
          status: response.status,
          statusText: response.statusText
        }
      })
    }
    
    const data = await response.json()

    
    // 返回格式化的数据（根据实际 API 响应结构）
    const result = {
      success: true,
      todayVisitors: data.visitors?.value || 0,
      onlineUsers: Math.max(1, Math.floor((data.visitors?.value || 0) * 0.1)), // 估算在线用户
      totalPageViews: data.pageviews?.value || 0,
      totalVisits: data.visits?.value || 0,
      bounceRate: data.bounces?.value || 0,
      totalTime: data.totaltime?.value || 0,
      source: 'umami',
      rawData: data,
      timestamp: new Date().toISOString()
    }
    
    res.status(200).json(result)
  } catch (error) {
    console.error('Umami API 代理错误:', error)
    res.status(500).json({ 
      error: 'Failed to fetch Umami data',
      message: error.message,
      success: false,
      todayVisitors: 0,
      onlineUsers: 1 
    })
  }
}
