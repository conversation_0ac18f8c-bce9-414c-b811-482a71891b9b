import { siteConfig } from '@/lib/config'
import { loadExternalResource } from '@/lib/utils'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  // 切到下一首
  const playNext = (aplayerInstance) => {
    if (aplayerInstance && aplayerInstance.list) {
      const { index, audios } = aplayerInstance.list;
      let nextIndex = index + 1;
      if (nextIndex >= audios.length) {
        nextIndex = 0; // 循环播放
      }
      aplayerInstance.list.switch(nextIndex);
      if (autoPlay) {
        aplayerInstance.play();
      }
    }
  }
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')
  const musicMetingCDNUrl = siteConfig(
    'MUSIC_PLAYER_METING_CDN_URL',
    'https://cdnjs.cloudflare.com/ajax/libs/meting/2.0.1/Meting.min.js'
  )

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable) {
      return
    }

    try {
      // 使用新的资源加载器，优先本地资源
      const { loadResourceWithFallback } = await import('@/lib/utils/resourceLoader')

      // 加载 APlayer
      await loadResourceWithFallback('/js/aplayer.min.js', 'js')

      // 如果启用了 MetingJS，也加载它
      if (musicMetingEnable) {
        await loadResourceWithFallback('/js/meting.min.js', 'js')
      }
    } catch (error) {
      console.error('音乐播放器资源加载失败:', error)
      return
    }

    if (!meting && window.APlayer) {
      const aplayerInstance = new window.APlayer({
        container: ref.current,
        fixed: true,
        lrcType: lrcType,
        autoplay: autoPlay,
        order: order,
        audio: audio,
        preload: 'metadata',
        volume: 1.0,
        mutex: true,
        listFolded: false,
        listMaxHeight: '300px',
        storageName: 'aplayer-setting',
        bufferTime: 15,
        errorHandler: function(error) {
          console.warn('APlayer播放错误:', error)
          // 自动跳到下一首
          playNext(aplayerInstance);
        }
      });
      // 监听播放结束事件，不做任何处理，APlayer会自动切到下一首
      // 监听音频错误事件，自动跳过
      aplayerInstance.on('error', () => {
        playNext(aplayerInstance);
      });
      setPlayer(aplayerInstance);
    }
  }

  useEffect(() => {
    initMusicPlayer()
    return () => {
      setPlayer(undefined)
    }
  }, [])

  // 加载CSS样式
  useEffect(() => {
    if (musicPlayerEnable) {
      const loadCSS = async () => {
        try {
          const { loadResourceWithFallback } = await import('@/lib/utils/resourceLoader')
          await loadResourceWithFallback('/css/aplayer.min.css', 'css')
        } catch (error) {
          console.warn('APlayer CSS加载失败:', error)
        }
      }

      loadCSS()
    }
  }, [musicPlayerEnable])

  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
