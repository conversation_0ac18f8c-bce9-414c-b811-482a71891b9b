# 环境变量 @see https://www.nextjs.cn/docs/basic-features/environment-variables

# 可在此添加环境变量，去掉最左边的（# ）注释即可
# Notion页面ID,必须
# NOTION_PAGE_ID=097e5f674880459d8e1b4407758dc4fb

# 自定义的Gemini聊天机器人
# NEXT_PUBLIC_GEMINI_CHAT_ENABLED=true
# NEXT_PUBLIC_GEMINI_API_URL=https://gemini-api.wobys.dpdns.org

# ChatAI Workers 配置
NEXT_PUBLIC_CHATAI_WORKER_URL=https://wobchatai.wobys.dpdns.org

# 非必须
# NEXT_PUBLIC_VERSION=
# NEXT_PUBLIC_PSEUDO_STATIC=
# NEXT_PUBLIC_REVALIDATE_SECOND=  
# NEXT_PUBLIC_THEME=matery
# NEXT_PUBLIC_THEME_SWITCH=
# NEXT_PUBLIC_LANG=
# NEXT_PUBLIC_APPEARANCE=
# NEXT_PUBLIC_APPEARANCE_DARK_TIME=
# NEXT_PUBLIC_GREETING_WORDS=
# NEXT_PUBLIC_CUSTOM_MENU=
# NEXT_PUBLIC_AUTHOR=
# NEXT_PUBLIC_BIO=
# NEXT_PUBLIC_LINK=
# NEXT_PUBLIC_KEYWORD=
# NEXT_PUBLIC_CONTACT_EMAIL=
# NEXT_PUBLIC_CONTACT_WEIBO=
# NEXT_PUBLIC_CONTACT_TWITTER= 
# NEXT_PUBLIC_CONTACT_GITHUB=
# NEXT_PUBLIC_CONTACT_TELEGRAM=
# NEXT_PUBLIC_CONTACT_LINKEDIN=
# NEXT_PUBLIC_CONTACT_INSTAGRAM=
# NEXT_PUBLIC_CONTACT_BILIBILI=
# NEXT_PUBLIC_CONTACT_YOUTUBE=
# NEXT_PUBLIC_FAVICON=
# NEXT_PUBLIC_FONT_STYLE=
# NEXT_PUBLIC_FONT_URL=
# NEXT_PUBLIC_FONT_SANS=
# NEXT_PUBLIC_FONT_SERIF=
# NEXT_PUBLIC_FONT_AWESOME_PATH=
# NEXT_PUBLIC_PRISM_THEME_PREFIX_PATH=  
# NEXT_PUBLIC_PRISM_THEME_SWITCH=
# NEXT_PUBLIC_PRISM_THEME_LIGHT_PATH=   
# NEXT_PUBLIC_PRISM_THEME_DARK_PATH=
# NEXT_PUBLIC_CODE_MAC_BAR=
# NEXT_PUBLIC_CODE_LINE_NUMBERS=  
# NEXT_PUBLIC_CODE_COLLAPSE=
# NEXT_PUBLIC_CODE_COLLAPSE_EXPAND_DEFAULT=
# NEXT_PUBLIC_MERMAID_CDN=
# NEXT_PUBLIC_QR_CODE_CDN=
# NEXT_PUBLIC_BACKGROUND_LIGHT=   
# NEXT_PUBLIC_BACKGROUND_DARK=
# NEXT_PUBLIC_SUB_PATH=
# NEXT_PUBLIC_POST_SHARE_BAR=
# NEXT_PUBLIC_POST_SHARE_SERVICES=
# NEXT_PUBLIC_POST_URL_PREFIX=  
# NEXT_PUBLIC_POST_LIST_STYLE=
# NEXT_PUBLIC_POST_PREVIEW=
# NEXT_PUBLIC_POST_RECOMMEND_COUNT=
# NEXT_PUBLIC_POSTS_PER_PAGE=
# NEXT_PUBLIC_POST_SORT_BY=
# NEXT_PUBLIC_ALGOLIA_APP_ID=
# ALGOLIA_ADMIN_APP_KEY=
# NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_APP_KEY=    
# NEXT_PUBLIC_ALGOLIA_INDEX=
# NEXT_PUBLIC_PREVIEW_CATEGORY_COUNT=  
# NEXT_PUBLIC_PREVIEW_TAG_COUNT=
# NEXT_PUBLIC_POST_TITLE_ICON=
# NEXT_PUBLIC_POST_DISABLE_GALLERY_CLICK=   
# NEXT_PUBLIC_FIREWORKS=
# NEXT_PUBLIC_FIREWORKS_COLOR=
# NEXT_PUBLIC_SAKURA=
# NEXT_PUBLIC_NEST=
# NEXT_PUBLIC_FLUTTERINGRIBBON=
# NEXT_PUBLIC_RIBBON=
# NEXT_PUBLIC_STARRY_SKY=
# NEXT_PUBLIC_CHATBASE_ID= # 已禁用，使用 ChatAI 替代
# NEXT_PUBLIC_WEB_WHIZ_ENABLED=
# NEXT_PUBLIC_WEB_WHIZ_BASE_URL=
# NEXT_PUBLIC_WEB_WHIZ_CHAT_BOT_ID=  
# NEXT_PUBLIC_WIDGET_PET=
# NEXT_PUBLIC_WIDGET_PET_LINK=
# NEXT_PUBLIC_WIDGET_PET_SWITCH_THEME=
# NEXT_PUBLIC_MUSIC_PLAYER=
# NEXT_PUBLIC_MUSIC_PLAYER_VISIBLE=
# NEXT_PUBLIC_MUSIC_PLAYER_AUTO_PLAY=    
# NEXT_PUBLIC_MUSIC_PLAYER_LRC_TYPE=
# NEXT_PUBLIC_MUSIC_PLAYER_CDN_URL=
# NEXT_PUBLIC_MUSIC_PLAYER_ORDER=
# NEXT_PUBLIC_MUSIC_PLAYER_AUDIO_LIST=
# NEXT_PUBLIC_MUSIC_PLAYER_METING=
# NEXT_PUBLIC_MUSIC_PLAYER_METING_SERVER=
# NEXT_PUBLIC_MUSIC_PLAYER_METING_ID=
# NEXT_PUBLIC_MUSIC_PLAYER_METING_LRC_TYPE=  
# NEXT_PUBLIC_COMMENT_ARTALK_SERVER=
# NEXT_PUBLIC_COMMENT_ARTALK_JS= 
# NEXT_PUBLIC_COMMENT_ARTALK_CSS=   
# NEXT_PUBLIC_COMMENT_ENV_ID=  
# NEXT_PUBLIC_COMMENT_TWIKOO_COUNT_ENABLE=
# NEXT_PUBLIC_COMMENT_TWIKOO_CDN_URL=
# NEXT_PUBLIC_COMMENT_UTTERRANCES_REPO=    
# NEXT_PUBLIC_COMMENT_GISCUS_REPO=
# NEXT_PUBLIC_COMMENT_GISCUS_REPO_ID=
# NEXT_PUBLIC_COMMENT_GISCUS_CATEGORY_ID=
# NEXT_PUBLIC_COMMENT_GISCUS_MAPPING=
# NEXT_PUBLIC_COMMENT_GISCUS_REACTIONS_ENABLED=
# NEXT_PUBLIC_COMMENT_GISCUS_EMIT_METADATA=
# NEXT_PUBLIC_COMMENT_GISCUS_INPUT_POSITION=
# NEXT_PUBLIC_COMMENT_GISCUS_LANG=     
# NEXT_PUBLIC_COMMENT_GISCUS_LOADING=
# NEXT_PUBLIC_COMMENT_GISCUS_CROSSORIGIN=
# NEXT_PUBLIC_COMMENT_CUSDIS_APP_ID=  
# NEXT_PUBLIC_COMMENT_CUSDIS_HOST=
# NEXT_PUBLIC_COMMENT_CUSDIS_SCRIPT_SRC=
# NEXT_PUBLIC_COMMENT_GITALK_REPO=
# NEXT_PUBLIC_COMMENT_GITALK_OWNER=    
# NEXT_PUBLIC_COMMENT_GITALK_ADMIN=  
# NEXT_PUBLIC_COMMENT_GITALK_CLIENT_ID=
# NEXT_PUBLIC_COMMENT_GITALK_CLIENT_SECRET=
# NEXT_PUBLIC_COMMENT_GITALK_JS_CDN_URL=
# NEXT_PUBLIC_COMMENT_GITALK_CSS_CDN_URL=   
# NEXT_PUBLIC_COMMENT_GITTER_ROOM=  
# NEXT_PUBLIC_COMMENT_DAO_VOICE_ID=
# NEXT_PUBLIC_COMMENT_TIDIO_ID=
# NEXT_PUBLIC_VALINE_CDN=    
# NEXT_PUBLIC_VALINE_ID=
# NEXT_PUBLIC_VALINE_KEY=
# NEXT_PUBLIC_VALINE_SERVER_URLS=
# NEXT_PUBLIC_VALINE_PLACEHOLDER=
# NEXT_PUBLIC_WALINE_SERVER_URL=
# NEXT_PUBLIC_WALINE_RECENT=
# NEXT_PUBLIC_WEBMENTION_ENABLE=
# NEXT_PUBLIC_WEBMENTION_AUTH=
# NEXT_PUBLIC_WEBMENTION_HOSTNAME=
# NEXT_PUBLIC_TWITTER_USERNAME=
# NEXT_PUBLIC_WEBMENTION_TOKEN= 
# NEXT_PUBLIC_ANALYTICS_VERCEL=    
# NEXT_PUBLIC_ANALYTICS_BUSUANZI_ENABLE=   
# NEXT_PUBLIC_ANALYTICS_BAIDU_ID=  
# NEXT_PUBLIC_ANALYTICS_CNZZ_ID=   
# NEXT_PUBLIC_ANALYTICS_GOOGLE_ID=
# NEXT_PUBLIC_ANALYTICS_ACKEE_TRACKER=
# NEXT_PUBLIC_ANALYTICS_ACKEE_DATA_SERVER=  
# NEXT_PUBLIC_ANALYTICS_ACKEE_DOMAIN_ID=
# NEXT_PUBLIC_SEO_GOOGLE_SITE_VERIFICATION=     
# NEXT_PUBLIC_SEO_BAIDU_SITE_VERIFICATION=    
# NEXT_PUBLIC_ADSENSE_GOOGLE_ID=
# NEXT_PUBLIC_ADSENSE_GOOGLE_TEST=  
# NEXT_PUBLIC_ADSENSE_GOOGLE_SLOT_IN_ARTICLE=
# NEXT_PUBLIC_ADSENSE_GOOGLE_SLOT_FLOW=
# NEXT_PUBLIC_ADSENSE_GOOGLE_SLOT_NATIVE=     
# NEXT_PUBLIC_ADSENSE_GOOGLE_SLOT_AUTO=  
# NEXT_PUBLIC_WWAD_ID=    
# NEXT_PUBLIC_WWADS_AD_BLOCK_DETECT=
# NEXT_PUBLIC_NOTION_PROPERTY_PASSWORD=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE_POST=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE_PAGE=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE_NOTICE=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE_MENU=
# NEXT_PUBLIC_NOTION_PROPERTY_TYPE_SUB_MENU=
# NEXT_PUBLIC_NOTION_PROPERTY_TITLE=
# NEXT_PUBLIC_NOTION_PROPERTY_STATUS=   
# NEXT_PUBLIC_NOTION_PROPERTY_STATUS_PUBLISH=
# NEXT_PUBLIC_NOTION_PROPERTY_STATUS_INVISIBLE=    
# NEXT_PUBLIC_NOTION_PROPERTY_SUMMARY=  
# NEXT_PUBLIC_NOTION_PROPERTY_SLUG=
# NEXT_PUBLIC_NOTION_PROPERTY_CATEGORY=   
# NEXT_PUBLIC_NOTION_PROPERTY_DATE=
# NEXT_PUBLIC_NOTION_PROPERTY_TAGS=
# NEXT_PUBLIC_NOTION_PROPERTY_ICON=
# NEXT_PUBLIC_ENABLE_RSS=  
# NEXT_PUBLIC_IS_TAG_COLOR_DISTINGUISHED=
# MAILCHIMP_LIST_ID=
# MAILCHIMP_API_KEY=    
# NEXT_PUBLIC_DEBUG=
# ENABLE_CACHE=
# VERCEL_ENV=
# NEXT_PUBLIC_VERSION=
# NEXT_BUILD_STANDALONE=
