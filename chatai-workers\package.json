{"name": "chatai-workers", "version": "1.0.0", "description": "ChatAI service powered by Cloudflare Workers", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env development", "deploy:production": "wrangler deploy --env production", "tail": "wrangler tail", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["chatai", "cloudflare-workers", "ai-chat", "file-upload", "url-parser"], "author": "wob", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20231218.0", "wrangler": "^3.22.1", "jest": "^29.7.0", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "dependencies": {"itty-router": "^4.0.23", "hono": "^3.12.0"}}