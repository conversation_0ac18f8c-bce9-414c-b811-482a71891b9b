/**
 * 移除在线用户API端点
 * 处理用户离开时的清理工作
 */

import { onlineUserManager } from '@/lib/firebase'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { userId, action } = req.body

    if (!userId || action !== 'remove') {
      return res.status(400).json({ error: 'Invalid request' })
    }

    // 从Firebase移除用户
    await onlineUserManager.removeUser(userId)
    
    console.log(`用户 ${userId} 已从在线列表移除`)
    
    res.status(200).json({ 
      success: true, 
      message: 'User removed successfully' 
    })

  } catch (error) {
    console.error('移除在线用户失败:', error)
    res.status(500).json({ 
      error: 'Failed to remove user',
      message: error.message 
    })
  }
}
