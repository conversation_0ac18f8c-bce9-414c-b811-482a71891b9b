/**
 * 图片相关配置
 *
 * eg: images.unsplash.com(notion图床的所有图片都会替换),如果你在 notion 里已经添加了一个随机图片 url，恰巧那个服务跑路或者挂掉，想一键切换所有配图可以将该 url 配置在这里
 * 默认下会将你上传到 notion的主页封面图和头像也给替换，建议将主页封面图和头像放在其他图床，在 notion 里配置 link 即可。
 */

module.exports = {
  // ⬇️⬇️⬇️【核心修改】请务必将此处的 URL 替换为您自己部署的 Worker 地址！⬇️⬇️⬇️
  NOTION_HOST: 'https://noso.zhiqiao.dpdns.org',

  // 当用户点击图片放大时，请求这个宽度的图片。可以保留。
  IMAGE_ZOOM_IN_WIDTH: 1920,

  // --- 以下是前端功能，根据您的喜好配置 ---
  RANDOM_IMAGE_URL: '',
  RANDOM_IMAGE_REPLACE_TEXT: 'images.unsplash.com',
  IMG_LAZY_LOAD_PLACEHOLDER: 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==',
  IMG_SHADOW: true,

  // --- 以下配置项已由 Worker 智能处理，可以安全地删除或注释掉 ---
  // IMAGE_COMPRESS_WIDTH: 1200,
  // IMG_COMPRESS_WIDTH: 1000,
  
  // 历史遗留配置，保持默认即可
  IMG_URL_TYPE: 'Notion',
}
