<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多服务统计系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .service-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        .service-card h3 {
            margin-top: 0;
            color: #666;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
        }
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3730a3;
        }
        .data-display {
            background: #4f46e5;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .data-item {
            display: inline-block;
            margin: 0 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 多服务统计系统测试</h1>
        
        <div class="data-display">
            <div class="data-item">
                <span>📈今日访问人数:</span>
                <span id="today-visitors">-</span>
            </div>
            <div class="data-item">
                <span>📊当前在线人数:</span>
                <span id="online-users">-</span>
            </div>
            <div class="data-item">
                <span>数据源:</span>
                <span id="data-source">⏳</span>
            </div>
        </div>

        <div class="service-status">
            <div class="service-card">
                <h3>📊 Umami Analytics (主力)</h3>
                <div id="umami-status" class="status loading">测试中...</div>
                <p>网站ID: 8e485e40-2d2b-44b2-b792-7d00bbc235dd</p>
                <button class="test-button" onclick="testUmami()">测试 Umami</button>
            </div>

            <div class="service-card">
                <h3>🔥 Firebase (备用1)</h3>
                <div id="firebase-status" class="status loading">测试中...</div>
                <p>状态: 待配置</p>
                <button class="test-button" onclick="testFirebase()">测试 Firebase</button>
            </div>

            <div class="service-card">
                <h3>▲ Vercel Analytics (备用2)</h3>
                <div id="vercel-status" class="status loading">测试中...</div>
                <p>状态: API 端点已创建</p>
                <button class="test-button" onclick="testVercel()">测试 Vercel</button>
            </div>

            <div class="service-card">
                <h3>💾 本地存储 (最后备用)</h3>
                <div id="local-status" class="status success">✅ 可用</div>
                <p>状态: 始终可用</p>
                <button class="test-button" onclick="testLocal()">测试本地存储</button>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>🔧 测试操作</h3>
            <button class="test-button" onclick="testAllServices()">测试所有服务</button>
            <button class="test-button" onclick="simulateFailover()">模拟故障切换</button>
            <button class="test-button" onclick="clearLocalData()">清空本地数据</button>
            <button class="test-button" onclick="window.open('/api/umami-test-simple', '_blank')">Umami 详细测试</button>
            <button class="test-button" onclick="window.open('/api/umami-debug', '_blank')">Umami 调试信息</button>
        </div>

        <div id="test-log" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            <strong>测试日志:</strong><br>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testUmami() {
            log('测试 Umami Analytics...');
            const statusDiv = document.getElementById('umami-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = '测试中...';

            try {
                const response = await fetch('/api/umami-stats');

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = '✅ 连接成功';
                        log(`Umami 数据: ${JSON.stringify(data)}`);

                        document.getElementById('today-visitors').textContent = data.todayVisitors || 0;
                        document.getElementById('online-users').textContent = data.onlineUsers || 1;
                        document.getElementById('data-source').textContent = '📊';
                    } else {
                        throw new Error(data.error || 'API 返回失败');
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 连接失败';
                log(`Umami 错误: ${error.message}`);
            }
        }

        async function testFirebase() {
            log('测试 Firebase...');
            const statusDiv = document.getElementById('firebase-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = '测试中...';

            try {
                // 测试 Firebase 连接
                const response = await fetch('/api/firebase-test');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = '✅ 连接成功';
                        log(`Firebase 数据: ${JSON.stringify(data)}`);

                        document.getElementById('today-visitors').textContent = data.todayVisitors || 0;
                        document.getElementById('online-users').textContent = data.onlineUsers || 1;
                        document.getElementById('data-source').textContent = '🔥';
                    } else {
                        throw new Error(data.error || 'Firebase 连接失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 连接失败';
                log(`Firebase 错误: ${error.message}`);
            }
        }

        async function testVercel() {
            log('测试 Vercel Analytics...');
            const statusDiv = document.getElementById('vercel-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = '测试中...';

            try {
                const response = await fetch('/api/vercel-stats');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ API 可用';
                    log(`Vercel 数据: ${JSON.stringify(data)}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ API 失败';
                log(`Vercel 错误: ${error.message}`);
            }
        }

        function testLocal() {
            log('测试本地存储...');
            const statusDiv = document.getElementById('local-status');
            
            try {
                const today = new Date().toDateString();
                const userId = 'test_user_' + Date.now();
                const todayKey = `visitors_${today}`;
                
                let visitors = JSON.parse(localStorage.getItem(todayKey) || '[]');
                if (!visitors.includes(userId)) {
                    visitors.push(userId);
                    localStorage.setItem(todayKey, JSON.stringify(visitors));
                }
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 正常工作';
                log(`本地存储: 今日访客 ${visitors.length} 人`);
                
                document.getElementById('today-visitors').textContent = visitors.length;
                document.getElementById('online-users').textContent = '1';
                document.getElementById('data-source').textContent = '💾';
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 存储失败';
                log(`本地存储错误: ${error.message}`);
            }
        }

        async function testAllServices() {
            log('开始测试所有服务...');
            await testUmami();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testFirebase();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testVercel();
            await new Promise(resolve => setTimeout(resolve, 1000));
            testLocal();
            log('所有服务测试完成');
        }

        function simulateFailover() {
            log('模拟故障切换场景...');
            log('1. Umami 不可用 → 尝试 Firebase');
            log('2. Firebase 未配置 → 尝试 Vercel');
            log('3. Vercel 可用 → 使用 Vercel 数据');
            log('4. 如果都失败 → 使用本地存储');
            testLocal();
        }

        function clearLocalData() {
            const keys = Object.keys(localStorage);
            let cleared = 0;
            keys.forEach(key => {
                if (key.startsWith('visitors_') || key === 'visitor_id') {
                    localStorage.removeItem(key);
                    cleared++;
                }
            });
            log(`清空了 ${cleared} 个本地存储项`);
        }

        // 页面加载时自动测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(testAllServices, 1000);
        });
    </script>
</body>
</html>
