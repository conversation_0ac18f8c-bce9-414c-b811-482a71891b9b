/**
 * 全面的API测试脚本
 * 测试项目中所有API端点的可用性和数据真实性
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// API端点列表
const API_ENDPOINTS = [
  {
    name: 'Cache API',
    path: '/api/cache',
    method: 'GET',
    description: '缓存清理API'
  },
  {
    name: 'Firebase Test',
    path: '/api/firebase-test',
    method: 'GET',
    description: 'Firebase实时数据库测试'
  },
  {
    name: 'Remove Online User',
    path: '/api/remove-online-user',
    method: 'POST',
    description: '移除在线用户',
    body: { userId: 'test-user-123', action: 'remove' }
  },
  {
    name: 'Subscribe',
    path: '/api/subscribe',
    method: 'POST',
    description: '邮件订阅',
    body: { email: '<EMAIL>', firstName: 'Test', lastName: 'User' }
  },
  {
    name: 'Umami Debug',
    path: '/api/umami-debug',
    method: 'GET',
    description: 'Umami Analytics调试信息'
  },
  {
    name: 'Umami Stats',
    path: '/api/umami-stats',
    method: 'GET',
    description: 'Umami Analytics统计数据'
  },
  {
    name: 'Umami Test Simple',
    path: '/api/umami-test-simple',
    method: 'GET',
    description: 'Umami简单连接测试'
  },
  {
    name: 'User Auth',
    path: '/api/user',
    method: 'GET',
    description: 'Clerk用户认证测试'
  },
  {
    name: 'Vercel Stats',
    path: '/api/vercel-stats',
    method: 'GET',
    description: 'Vercel Analytics统计数据'
  }
];

/**
 * 测试单个API端点
 */
async function testAPI(endpoint) {
  const url = `${BASE_URL}${endpoint.path}`;
  
  console.log(`\n🔍 测试: ${endpoint.name}`);
  console.log(`📍 URL: ${url}`);
  console.log(`📝 描述: ${endpoint.description}`);
  
  try {
    const options = {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Test-Script'
      }
    };
    
    if (endpoint.body) {
      options.body = JSON.stringify(endpoint.body);
    }
    
    const startTime = Date.now();
    const response = await fetch(url, options);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    let responseData;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }
    
    const result = {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      responseTime: `${responseTime}ms`,
      contentType,
      data: responseData
    };
    
    if (response.ok) {
      console.log(`✅ 成功 (${response.status}) - ${responseTime}ms`);
      
      // 分析数据真实性
      analyzeDataReality(endpoint.name, responseData);
    } else {
      console.log(`❌ 失败 (${response.status}): ${response.statusText}`);
      if (responseData) {
        console.log(`📄 错误详情:`, JSON.stringify(responseData, null, 2));
      }
    }
    
    return result;
    
  } catch (error) {
    console.log(`💥 网络错误: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 分析数据真实性
 */
function analyzeDataReality(apiName, data) {
  console.log(`📊 数据分析:`);
  
  switch (apiName) {
    case 'Firebase Test':
      if (data.success) {
        console.log(`  👥 今日访客: ${data.todayVisitors}`);
        console.log(`  🟢 在线用户: ${data.onlineUsers}`);
        console.log(`  📡 数据源: ${data.source}`);
        
        // 检查数据合理性
        if (data.todayVisitors >= 0 && data.onlineUsers >= 0) {
          console.log(`  ✅ 数据格式正常`);
        } else {
          console.log(`  ⚠️  数据值异常`);
        }
      }
      break;
      
    case 'Umami Stats':
      if (data.success) {
        console.log(`  👥 今日访客: ${data.data?.todayVisitors || 'N/A'}`);
        console.log(`  🟢 在线用户: ${data.data?.onlineUsers || 'N/A'}`);
        console.log(`  📈 页面浏览: ${data.data?.totalPageViews || 'N/A'}`);
        console.log(`  📡 数据源: ${data.source || 'umami'}`);
      } else {
        console.log(`  ❌ Umami连接失败: ${data.error}`);
      }
      break;
      
    case 'Vercel Stats':
      if (data.success) {
        console.log(`  📊 Vercel数据可用`);
        console.log(`  📡 数据源: vercel`);
      } else {
        console.log(`  ⚠️  Vercel API限制或配置问题`);
      }
      break;
      
    case 'Umami Debug':
      if (data.tests) {
        const successfulTests = data.tests.filter(test => test.success).length;
        const totalTests = data.tests.length;
        console.log(`  🧪 测试通过: ${successfulTests}/${totalTests}`);
        
        data.tests.forEach(test => {
          const status = test.success ? '✅' : '❌';
          console.log(`    ${status} ${test.name}`);
        });
      }
      break;
      
    default:
      if (typeof data === 'object' && data !== null) {
        console.log(`  📦 返回对象，包含 ${Object.keys(data).length} 个字段`);
      } else {
        console.log(`  📄 返回类型: ${typeof data}`);
      }
  }
}

/**
 * 检查开发服务器是否运行
 */
async function checkServerRunning() {
  try {
    const response = await fetch(BASE_URL);
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 开始API全面测试');
  console.log('=' * 60);
  
  // 检查服务器状态
  console.log('\n🔍 检查开发服务器状态...');
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    console.log('❌ 开发服务器未运行！');
    console.log('请先运行: npm run dev');
    return;
  }
  
  console.log('✅ 开发服务器正在运行');
  
  const results = [];
  let successCount = 0;
  let failCount = 0;
  
  // 测试所有API端点
  for (const endpoint of API_ENDPOINTS) {
    const result = await testAPI(endpoint);
    results.push({ endpoint: endpoint.name, ...result });
    
    if (result.success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 生成测试报告
  console.log('\n' + '=' * 60);
  console.log('📋 测试总结报告');
  console.log('=' * 60);
  
  console.log(`\n📊 总体统计:`);
  console.log(`  ✅ 成功: ${successCount}`);
  console.log(`  ❌ 失败: ${failCount}`);
  console.log(`  📁 总计: ${API_ENDPOINTS.length}`);
  console.log(`  📈 成功率: ${((successCount / API_ENDPOINTS.length) * 100).toFixed(1)}%`);
  
  console.log(`\n🔍 详细结果:`);
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const time = result.responseTime || 'N/A';
    console.log(`  ${status} ${result.endpoint} (${time})`);
  });
  
  console.log(`\n💡 建议:`);
  if (failCount > 0) {
    console.log(`  • 检查失败的API配置和依赖`);
    console.log(`  • 确认环境变量设置正确`);
    console.log(`  • 检查第三方服务连接状态`);
  } else {
    console.log(`  • 所有API运行正常！`);
    console.log(`  • 可以进行生产环境部署`);
  }
  
  console.log('\n✨ API测试完成！');
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testAPI };
