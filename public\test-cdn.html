<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <h1>CDN连接测试</h1>
    <p>此页面用于测试各个CDN的连接状态</p>
    
    <div id="test-results"></div>
    
    <script>
        const cdnTests = [
            { name: 'PrismJS AutoLoader (elemecdn)', url: 'https://npm.elemecdn.com/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js', type: 'js' },
            { name: 'PrismJS AutoLoader (本地)', url: '/js/prism-autoloader.min.js', type: 'local' },
            { name: 'PrismJS Components (elemecdn)', url: 'https://npm.elemecdn.com/prismjs@1.29.0/components/', type: 'js' },
            { name: 'APlayer JS (unpkg)', url: 'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js', type: 'js' },
            { name: 'APlayer CSS (unpkg)', url: 'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css', type: 'css' },
            { name: 'APlayer JS (jsdelivr)', url: 'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js', type: 'js' },
            { name: 'APlayer CSS (jsdelivr)', url: 'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css', type: 'css' },
            { name: 'APlayer JS (cdnjs)', url: 'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.js', type: 'js' },
            { name: 'APlayer CSS (cdnjs)', url: 'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.css', type: 'css' },
            { name: 'Live2D Audio (jsdelivr)', url: 'https://cdn.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/start.mp3', type: 'audio' },
            { name: 'Live2D Audio (gcore)', url: 'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/start.mp3', type: 'audio' }
        ];
        
        const resultsContainer = document.getElementById('test-results');
        
        function createTestItem(test) {
            const div = document.createElement('div');
            div.className = 'test-item loading';
            div.innerHTML = `<strong>${test.name}</strong><br>URL: ${test.url}<br>状态: 测试中...`;
            return div;
        }
        
        function updateTestItem(div, status, message) {
            div.className = `test-item ${status}`;
            const content = div.innerHTML.split('<br>状态: ')[0];
            div.innerHTML = `${content}<br>状态: ${message}`;
        }
        
        async function testCDN(test) {
            const div = createTestItem(test);
            resultsContainer.appendChild(div);
            
            try {
                if (test.type === 'audio') {
                    // 测试音频文件
                    const audio = new Audio();
                    await new Promise((resolve, reject) => {
                        audio.oncanplaythrough = resolve;
                        audio.onerror = reject;
                        audio.src = test.url;
                    });
                    updateTestItem(div, 'success', '✅ 连接成功');
                } else if (test.type === 'local') {
                    // 测试本地文件
                    const response = await fetch(test.url);
                    if (response.ok) {
                        updateTestItem(div, 'success', '✅ 本地文件可用');
                    } else {
                        updateTestItem(div, 'error', `❌ 本地文件不可用 HTTP ${response.status}`);
                    }
                } else {
                    // 测试JS/CSS文件
                    const response = await fetch(test.url, { method: 'HEAD' });
                    if (response.ok) {
                        updateTestItem(div, 'success', '✅ 连接成功');
                    } else {
                        updateTestItem(div, 'error', `❌ HTTP ${response.status}`);
                    }
                }
            } catch (error) {
                updateTestItem(div, 'error', `❌ 连接失败: ${error.message}`);
            }
        }
        
        // 开始测试
        cdnTests.forEach(test => {
            setTimeout(() => testCDN(test), Math.random() * 1000);
        });
    </script>
</body>
</html>
