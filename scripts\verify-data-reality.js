/**
 * 数据真实性验证脚本
 * 深度验证API返回数据的真实性和一致性
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

/**
 * 测试Firebase数据的真实性
 */
async function testFirebaseReality() {
  console.log('\n🔥 Firebase数据真实性测试');
  console.log('=' * 40);
  
  try {
    const response = await fetch(`${BASE_URL}/api/firebase-test`);
    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Firebase连接成功`);
      console.log(`📊 今日访客: ${data.todayVisitors}`);
      console.log(`👥 在线用户: ${data.onlineUsers}`);
      console.log(`⏰ 时间戳: ${data.timestamp}`);
      
      // 验证数据合理性
      const validations = [
        {
          name: '访客数非负',
          test: data.todayVisitors >= 0,
          value: data.todayVisitors
        },
        {
          name: '在线用户非负',
          test: data.onlineUsers >= 0,
          value: data.onlineUsers
        },
        {
          name: '时间戳格式正确',
          test: !isNaN(Date.parse(data.timestamp)),
          value: data.timestamp
        },
        {
          name: '数据源正确',
          test: data.source === 'firebase',
          value: data.source
        }
      ];
      
      console.log('\n🧪 数据验证:');
      validations.forEach(validation => {
        const status = validation.test ? '✅' : '❌';
        console.log(`  ${status} ${validation.name}: ${validation.value}`);
      });
      
      return {
        success: true,
        todayVisitors: data.todayVisitors,
        onlineUsers: data.onlineUsers,
        isRealTime: true
      };
    } else {
      console.log(`❌ Firebase连接失败: ${data.message}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`💥 Firebase测试错误: ${error.message}`);
    return { success: false };
  }
}

/**
 * 测试Umami数据的真实性
 */
async function testUmamiReality() {
  console.log('\n📈 Umami Analytics数据真实性测试');
  console.log('=' * 40);
  
  try {
    // 测试基本统计
    const statsResponse = await fetch(`${BASE_URL}/api/umami-stats`);
    const statsData = await statsResponse.json();
    
    console.log(`📊 Umami统计数据:`);
    console.log(`  状态: ${statsData.success ? '成功' : '失败'}`);
    
    if (statsData.success && statsData.data) {
      console.log(`  今日访客: ${statsData.data.todayVisitors || 'N/A'}`);
      console.log(`  在线用户: ${statsData.data.onlineUsers || 'N/A'}`);
      console.log(`  页面浏览: ${statsData.data.totalPageViews || 'N/A'}`);
    }
    
    // 测试调试信息
    const debugResponse = await fetch(`${BASE_URL}/api/umami-debug`);
    const debugData = await debugResponse.json();
    
    console.log(`\n🔍 Umami连接测试:`);
    if (debugData.tests) {
      const successfulTests = debugData.tests.filter(test => test.success);
      console.log(`  成功测试: ${successfulTests.length}/${debugData.tests.length}`);
      
      // 检查关键连接
      const keyTests = ['Basic Connection (v1 API)', 'Website Info (v1)', 'Auth: X-Umami-API-Key (Correct)'];
      keyTests.forEach(testName => {
        const test = debugData.tests.find(t => t.name === testName);
        const status = test && test.success ? '✅' : '❌';
        console.log(`  ${status} ${testName}`);
      });
    }
    
    return {
      success: statsData.success,
      hasRealData: !!(statsData.data && (statsData.data.todayVisitors || statsData.data.totalPageViews)),
      connectionWorking: debugData.tests && debugData.tests.some(t => t.success)
    };
  } catch (error) {
    console.log(`💥 Umami测试错误: ${error.message}`);
    return { success: false };
  }
}

/**
 * 测试数据一致性
 */
async function testDataConsistency() {
  console.log('\n🔄 数据一致性测试');
  console.log('=' * 40);
  
  const results = [];
  
  // 多次调用同一API，检查数据一致性
  for (let i = 0; i < 3; i++) {
    try {
      const response = await fetch(`${BASE_URL}/api/firebase-test`);
      const data = await response.json();
      
      if (data.success) {
        results.push({
          call: i + 1,
          todayVisitors: data.todayVisitors,
          onlineUsers: data.onlineUsers,
          timestamp: new Date(data.timestamp)
        });
      }
      
      // 间隔1秒
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(`❌ 第${i + 1}次调用失败: ${error.message}`);
    }
  }
  
  if (results.length >= 2) {
    console.log(`📊 ${results.length}次调用结果:`);
    results.forEach(result => {
      console.log(`  调用${result.call}: 访客=${result.todayVisitors}, 在线=${result.onlineUsers}`);
    });
    
    // 检查访客数一致性（应该相同或递增）
    const visitorsConsistent = results.every((result, index) => {
      if (index === 0) return true;
      return result.todayVisitors >= results[index - 1].todayVisitors;
    });
    
    // 检查在线用户数合理性（可能有小幅变化）
    const onlineUsers = results.map(r => r.onlineUsers);
    const maxOnline = Math.max(...onlineUsers);
    const minOnline = Math.min(...onlineUsers);
    const onlineReasonable = (maxOnline - minOnline) <= 5; // 允许5以内的变化
    
    console.log(`\n✅ 一致性检查:`);
    console.log(`  ${visitorsConsistent ? '✅' : '❌'} 访客数据一致性`);
    console.log(`  ${onlineReasonable ? '✅' : '❌'} 在线用户数合理性`);
    
    return {
      consistent: visitorsConsistent && onlineReasonable,
      visitorsConsistent,
      onlineReasonable
    };
  } else {
    console.log(`❌ 数据不足，无法进行一致性检查`);
    return { consistent: false };
  }
}

/**
 * 测试实时性
 */
async function testRealTimeData() {
  console.log('\n⏱️  实时数据测试');
  console.log('=' * 40);
  
  try {
    // 模拟添加在线用户
    console.log('📝 模拟添加在线用户...');
    const addResponse = await fetch(`${BASE_URL}/api/remove-online-user`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: 'test-realtime-user', action: 'add' })
    });
    
    // 获取当前数据
    const beforeResponse = await fetch(`${BASE_URL}/api/firebase-test`);
    const beforeData = await beforeResponse.json();
    
    console.log(`📊 当前数据: 访客=${beforeData.todayVisitors}, 在线=${beforeData.onlineUsers}`);
    
    // 移除用户
    console.log('🗑️  移除测试用户...');
    const removeResponse = await fetch(`${BASE_URL}/api/remove-online-user`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: 'test-realtime-user', action: 'remove' })
    });
    
    if (removeResponse.ok) {
      console.log('✅ 用户移除操作成功');
    }
    
    // 等待一下再检查
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const afterResponse = await fetch(`${BASE_URL}/api/firebase-test`);
    const afterData = await afterResponse.json();
    
    console.log(`📊 操作后数据: 访客=${afterData.todayVisitors}, 在线=${afterData.onlineUsers}`);
    
    return {
      realTime: true,
      operationWorking: removeResponse.ok
    };
  } catch (error) {
    console.log(`💥 实时测试错误: ${error.message}`);
    return { realTime: false };
  }
}

/**
 * 主验证函数
 */
async function runDataVerification() {
  console.log('🔍 开始数据真实性验证');
  console.log('=' * 60);
  
  const results = {
    firebase: await testFirebaseReality(),
    umami: await testUmamiReality(),
    consistency: await testDataConsistency(),
    realTime: await testRealTimeData()
  };
  
  // 生成验证报告
  console.log('\n' + '=' * 60);
  console.log('📋 数据真实性验证报告');
  console.log('=' * 60);
  
  console.log('\n🔥 Firebase数据:');
  if (results.firebase.success) {
    console.log(`  ✅ 连接正常，数据真实`);
    console.log(`  📊 今日访客: ${results.firebase.todayVisitors}`);
    console.log(`  👥 在线用户: ${results.firebase.onlineUsers}`);
    console.log(`  ⚡ 实时更新: ${results.firebase.isRealTime ? '是' : '否'}`);
  } else {
    console.log(`  ❌ 连接失败或数据异常`);
  }
  
  console.log('\n📈 Umami Analytics:');
  if (results.umami.success) {
    console.log(`  ✅ API连接正常`);
    console.log(`  📊 有真实数据: ${results.umami.hasRealData ? '是' : '否'}`);
    console.log(`  🔗 连接测试: ${results.umami.connectionWorking ? '通过' : '失败'}`);
  } else {
    console.log(`  ❌ 连接失败`);
  }
  
  console.log('\n🔄 数据一致性:');
  if (results.consistency.consistent) {
    console.log(`  ✅ 数据一致性良好`);
  } else {
    console.log(`  ⚠️  数据一致性需要关注`);
  }
  
  console.log('\n⏱️  实时性:');
  if (results.realTime.realTime) {
    console.log(`  ✅ 实时数据更新正常`);
  } else {
    console.log(`  ⚠️  实时更新可能有延迟`);
  }
  
  // 总体评估
  const overallScore = [
    results.firebase.success,
    results.umami.connectionWorking,
    results.consistency.consistent,
    results.realTime.realTime
  ].filter(Boolean).length;
  
  console.log('\n🎯 总体评估:');
  console.log(`  📊 功能正常率: ${overallScore}/4 (${(overallScore/4*100).toFixed(1)}%)`);
  
  if (overallScore >= 3) {
    console.log(`  ✅ 数据质量优秀，可以正常使用`);
  } else if (overallScore >= 2) {
    console.log(`  ⚠️  数据质量良好，部分功能需要优化`);
  } else {
    console.log(`  ❌ 数据质量需要改进`);
  }
  
  console.log('\n✨ 验证完成！');
}

// 运行验证
if (require.main === module) {
  runDataVerification().catch(console.error);
}

module.exports = { runDataVerification };
