/**
 * Vercel Speed Insights 组件
 * 用于监控网站性能指标
 */

import { SpeedInsights } from '@vercel/speed-insights/next'
import { siteConfig } from '@/lib/config'

/**
 * Vercel Speed Insights 组件
 * 只在生产环境启用，避免开发环境的性能监控干扰
 */
export default function VercelSpeedInsights() {
  // 检查是否启用 Speed Insights
  const speedInsightsEnabled = siteConfig('ANALYTICS_VERCEL_SPEED_INSIGHTS', true)
  
  // 检查是否为生产环境
  const isProduction = process.env.NODE_ENV === 'production' || 
                      process.env.VERCEL_ENV === 'production'
  
  // 只在生产环境且启用的情况下加载
  if (!speedInsightsEnabled || !isProduction) {
    return null
  }

  return <SpeedInsights />
}
