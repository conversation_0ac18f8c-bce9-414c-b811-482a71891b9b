<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台过滤器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
        }
        .status.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .status.warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 控制台过滤器测试页面</h1>
        
        <div class="status info">
            <strong>说明:</strong> 打开浏览器开发者工具的控制台，然后点击下面的按钮测试过滤效果。
        </div>

        <div class="test-section">
            <h3>📊 过滤器状态</h3>
            <div id="filterStatus">检查中...</div>
            <button onclick="checkFilterStatus()">检查过滤器状态</button>
            <button onclick="toggleFilter()">切换过滤器</button>
        </div>

        <div class="test-section">
            <h3>🧪 应该被过滤的日志</h3>
            <p>这些日志在生产环境中应该被过滤掉：</p>
            <button onclick="testFilteredLogs()">测试被过滤日志</button>
            <button onclick="testLive2DLogs()">测试Live2D日志</button>
            <button onclick="testCacheLogs()">测试缓存日志</button>
            <button onclick="testResourceLogs()">测试资源加载日志</button>
        </div>

        <div class="test-section">
            <h3>✅ 应该保留的日志</h3>
            <p>这些日志应该正常显示：</p>
            <button onclick="testNormalLogs()">测试正常日志</button>
            <button onclick="testErrorLogs()">测试错误日志</button>
        </div>

        <div class="test-section">
            <h3>🔧 调试功能</h3>
            <button onclick="restoreConsole()">恢复原始控制台</button>
            <button onclick="getFilterCount()">查看过滤统计</button>
            <button onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="status warning">
            <strong>注意:</strong> 
            <ul>
                <li>在开发环境(localhost)中，过滤器默认禁用</li>
                <li>添加 <code>?console-filter=true</code> 参数可强制启用</li>
                <li>在生产环境中，过滤器自动启用</li>
            </ul>
        </div>
    </div>

    <!-- 加载控制台清理器 -->
    <script src="/js/console-cleaner.js"></script>

    <script>
        // 检查过滤器状态
        function checkFilterStatus() {
            const hostname = window.location.hostname;
            const isProduction = hostname !== 'localhost' && hostname !== '127.0.0.1';
            const urlParams = new URLSearchParams(window.location.search);
            const forceFilter = urlParams.get('console-filter') === 'true';
            
            let status = '';
            if (isProduction) {
                status = '🟢 生产环境 - 过滤器已启用';
            } else if (forceFilter) {
                status = '🟡 开发环境 - 过滤器强制启用';
            } else {
                status = '🔴 开发环境 - 过滤器已禁用';
            }
            
            document.getElementById('filterStatus').innerHTML = status;
            console.log('过滤器状态检查:', status);
        }

        // 切换过滤器
        function toggleFilter() {
            const urlParams = new URLSearchParams(window.location.search);
            const currentFilter = urlParams.get('console-filter');
            
            if (currentFilter === 'true') {
                urlParams.delete('console-filter');
            } else {
                urlParams.set('console-filter', 'true');
            }
            
            const newUrl = window.location.pathname + '?' + urlParams.toString();
            window.location.href = newUrl;
        }

        // 测试被过滤的日志
        function testFilteredLogs() {
            console.log('=== 开始测试被过滤日志 ===');
            console.log('Live2D 2.1.00_1');
            console.log('profile : Desktop');
            console.log('[PROFILE_NAME] = Desktop');
            console.log('APlayer v1.10.1 af84efb http://aplayer.js.org');
            console.log('执行操作 fetch_umami_stats, 尝试 1/4');
            console.log('操作 fetch_umami_stats 成功完成');
            console.log('=== 被过滤日志测试结束 ===');
        }

        // 测试Live2D日志
        function testLive2DLogs() {
            console.log('=== Live2D 日志测试 ===');
            console.log('Live2D 2.1.00_1');
            console.log('profile : Desktop');
            console.log('[PROFILE_NAME] = Desktop');
            console.log('[USE_ADJUST_TRANSLATION] = false');
            console.log('[USE_CACHED_POLYGON_IMAGE] = false');
            console.log('[EXPAND_W] = 2');
        }

        // 测试缓存日志
        function testCacheLogs() {
            console.log('=== 缓存日志测试 ===');
            console.log('缓存设置: umami_stats (umami, TTL: 120000ms)');
            console.log('缓存命中: firebase_stats (age: 28532ms)');
            console.log('统计数据更新: {source: "umami", todayVisitors: 12}');
        }

        // 测试资源加载日志
        function testResourceLogs() {
            console.log('=== 资源加载日志测试 ===');
            console.log('✅ 本地资源加载成功: /css/aplayer.min.css');
            console.log('✅ CDN备用资源加载成功: https://unpkg.com/aplayer');
        }

        // 测试正常日志
        function testNormalLogs() {
            console.log('=== 正常日志测试 ===');
            console.log('用户登录成功');
            console.log('页面加载完成');
            console.info('数据保存成功');
            console.warn('这是一个警告信息');
            console.log('=== 正常日志测试结束 ===');
        }

        // 测试错误日志
        function testErrorLogs() {
            console.log('=== 错误日志测试 ===');
            console.error('这是一个错误信息');
            console.warn('表单验证失败');
            console.error('网络请求超时');
        }

        // 恢复控制台
        function restoreConsole() {
            if (window.restoreConsole) {
                window.restoreConsole();
                console.log('✅ 控制台已恢复到原始状态');
            } else {
                console.log('❌ 恢复功能不可用');
            }
        }

        // 获取过滤统计
        function getFilterCount() {
            if (window.getFilteredLogCount) {
                const count = window.getFilteredLogCount();
                console.log(`📊 已过滤日志数量: ${count}`);
            } else {
                console.log('❌ 统计功能不可用');
            }
        }

        // 清空控制台
        function clearConsole() {
            console.clear();
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkFilterStatus();
            console.log('🧪 控制台过滤器测试页面已加载');
            console.log('请点击按钮测试各种日志过滤效果');
        };
    </script>
</body>
</html>
