/**
 * 重置访客数据API端点
 * 用于手动重置今日访客数据（仅用于测试和管理）
 */

import { onlineUserManager, cleanupManager } from '@/lib/firebase'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { action, confirm } = req.body

    // 安全检查
    if (confirm !== 'RESET_CONFIRMED') {
      return res.status(400).json({ 
        error: 'Missing confirmation',
        message: '需要确认参数 confirm: "RESET_CONFIRMED"'
      })
    }

    let result = {}

    switch (action) {
      case 'reset_today':
        // 重置今日访客数据
        await onlineUserManager.resetTodayVisitors()
        result.message = '今日访客数据已重置'
        break

      case 'cleanup_old':
        // 清理过期数据
        await onlineUserManager.cleanupOldVisitorData()
        result.message = '过期数据清理完成'
        break

      case 'cleanup_all':
        // 执行完整清理
        await cleanupManager.performCleanup()
        result.message = '完整数据清理完成'
        break

      case 'reset_local':
        // 提供清理本地存储的指导
        result.message = '请在浏览器控制台执行: localStorage.clear() 或刷新页面'
        result.script = `
          // 清理本地访客数据
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('visitors_') || key === 'visitor_id' || key === 'firebase_user_id') {
              localStorage.removeItem(key);
            }
          });
          console.log('本地访客数据已清理');
        `
        break

      default:
        return res.status(400).json({ 
          error: 'Invalid action',
          validActions: ['reset_today', 'cleanup_old', 'cleanup_all', 'reset_local']
        })
    }

    console.log(`数据重置操作: ${action} - ${result.message}`)
    
    res.status(200).json({ 
      success: true,
      action,
      ...result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('数据重置失败:', error)
    res.status(500).json({ 
      error: 'Reset failed',
      message: error.message 
    })
  }
}
