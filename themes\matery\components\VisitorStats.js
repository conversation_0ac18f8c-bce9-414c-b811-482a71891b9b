import { useEffect, useState, useRef } from 'react'
import { calculateOnlineUsers, smoothOnlineUsers } from '@/lib/onlineUserCalculator'
import { cacheManager } from '@/lib/cacheManager'
import { retryManager } from '@/lib/retryManager'

/**
 * 访问统计组件
 * 支持多数据源：Umami Analytics, Firebase, Vercel Analytics
 * 特性：9秒自动刷新，北京时间24点重置，静默运行
 */
const VisitorStats = () => {
  const [todayVisitors, setTodayVisitors] = useState(0)
  const [onlineUsers, setOnlineUsers] = useState(1)
  const [dataSource, setDataSource] = useState('loading')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [healthStatus, setHealthStatus] = useState({})

  // 用于平滑数据变化和定时器管理
  const previousOnlineUsers = useRef(null)
  const refreshInterval = useRef(null)
  const resetCheckInterval = useRef(null)
  const lastResetDate = useRef(null)

  useEffect(() => {
    // 初始化
    initializeStats()

    // 设置9秒自动刷新，使用最新缓存覆盖旧缓存
    refreshInterval.current = setInterval(() => {
      fetchRealTimeStats(true) // 强制刷新
    }, 9000)

    // 设置北京时间24点重置检查
    resetCheckInterval.current = setInterval(checkDailyReset, 60000)

    // 初始化重置日期
    lastResetDate.current = getCurrentBeijingDate()

    return () => {
      if (refreshInterval.current) clearInterval(refreshInterval.current)
      if (resetCheckInterval.current) clearInterval(resetCheckInterval.current)
      cleanupFirebaseSession()
      stopFirebaseCleanup()
    }
  }, [])

  // 获取北京时间日期
  const getCurrentBeijingDate = () => {
    const now = new Date()
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)) // UTC+8
    return beijingTime.toISOString().split('T')[0] // YYYY-MM-DD
  }

  // 检查是否需要每日重置
  const checkDailyReset = () => {
    const currentDate = getCurrentBeijingDate()
    if (lastResetDate.current !== currentDate) {
      performDailyReset()
      lastResetDate.current = currentDate
    }
  }

  // 执行每日重置
  const performDailyReset = () => {
    // 清除所有缓存
    cacheManager.clear()

    // 重置本地存储的访客数据
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('visitors_') || key.startsWith('visitor_cache_')) {
          localStorage.removeItem(key)
        }
      })
    }

    // 强制刷新数据
    fetchRealTimeStats(true)
  }

  // 统一初始化函数
  const initializeStats = () => {
    cleanupLocalVisitorData()
    initMultiServiceStats()
    initFirebaseSession()
    initFirebaseCleanup()
  }

  // 初始化多服务统计 - 混合策略
  const initMultiServiceStats = async () => {
    setIsLoading(true)
    setDataSource('loading')
    setError(null)

    try {
      // 首次加载强制刷新，避免使用过期缓存
      // 并行获取数据，强制刷新缓存
      const [umamiData, firebaseData, vercelData] = await Promise.allSettled([
        fetchUmamiStats(true), // 强制刷新
        fetchFirebaseStats(true), // 强制刷新
        fetchVercelStats(true) // 强制刷新
      ])

      // 混合策略：访客数优先Umami/Vercel，在线用户数优先Firebase
      let bestVisitorSource = null
      let bestOnlineSource = null

      // 选择最佳访客数据源
      if (umamiData.status === 'fulfilled' && umamiData.value.success) {
        bestVisitorSource = umamiData.value
      } else if (vercelData.status === 'fulfilled' && vercelData.value.success) {
        bestVisitorSource = vercelData.value
      } else if (firebaseData.status === 'fulfilled' && firebaseData.value.success) {
        bestVisitorSource = firebaseData.value
      }

      // 选择最佳在线用户数据源（优先Firebase）
      if (firebaseData.status === 'fulfilled' && firebaseData.value.success) {
        bestOnlineSource = firebaseData.value
      } else if (umamiData.status === 'fulfilled' && umamiData.value.success) {
        bestOnlineSource = umamiData.value
      } else if (vercelData.status === 'fulfilled' && vercelData.value.success) {
        bestOnlineSource = vercelData.value
      }

      // 组合最佳数据
      if (bestVisitorSource || bestOnlineSource) {
        const combinedData = {
          success: true,
          todayVisitors: bestVisitorSource?.todayVisitors || bestOnlineSource?.todayVisitors || 0,
          onlineUsers: bestOnlineSource?.onlineUsers || bestVisitorSource?.onlineUsers || 1,
          source: `${bestVisitorSource?.source || 'local'}+${bestOnlineSource?.source || 'local'}`,
          primarySource: bestVisitorSource?.source || bestOnlineSource?.source || 'local',
          onlineSource: bestOnlineSource?.source || 'estimated'
        }
        updateStats(combinedData)
        return
      }

      // 所有服务都失败，使用本地备用方案
      const localData = getLocalStats()
      updateStats(localData)

    } catch (error) {
      console.error('初始化统计数据失败:', error)
      setError(`初始化失败: ${error.message}`)

      // 使用本地备用方案
      const localData = getLocalStats()
      updateStats(localData)
    } finally {
      setIsLoading(false)
    }
  }

  // 更新统计数据的统一函数
  const updateStats = (data) => {
    setTodayVisitors(data.todayVisitors)

    // 平滑在线用户数变化
    const smoothedOnlineUsers = smoothOnlineUsers(
      data.onlineUsers,
      previousOnlineUsers.current,
      0.3
    )

    setOnlineUsers(smoothedOnlineUsers)
    previousOnlineUsers.current = smoothedOnlineUsers

    setDataSource(data.source)
  }



  // 1. Umami Analytics 数据获取（通过服务器端代理）- 不使用缓存
  const fetchUmamiStats = async () => {
    try {
      const result = await retryManager.executeWithRetry(
        async () => {
          const response = await fetch('/api/umami-stats?t=' + Date.now()) // 添加时间戳防止浏览器缓存
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
          return response.json()
        },
        'umami',
        { operationId: 'fetch_umami_stats' }
      )

      if (result.success) {
        // 使用智能算法计算在线用户数
        const calculatedOnlineUsers = calculateOnlineUsers({
          todayVisitors: result.todayVisitors,
          totalPageViews: result.totalPageViews,
          avgSessionTime: result.totalTime / Math.max(1, result.todayVisitors),
          source: 'umami'
        })

        const processedData = {
          success: true,
          todayVisitors: result.todayVisitors || 0,
          onlineUsers: calculatedOnlineUsers,
          rawOnlineUsers: result.onlineUsers,
          source: 'umami',
          rawData: result,
          timestamp: Date.now()
        }

        return processedData
      }
    } catch (error) {
      console.warn('Umami Analytics 获取失败:', error)
      setError(`Umami: ${error.message}`)
    }

    return { success: false }
  }

  // 2. Firebase Realtime Database 数据获取
  const fetchFirebaseStats = async (forceRefresh = false) => {
    const cacheKey = 'firebase_stats'

    // 如果不是强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        return cached
      }
    }

    try {
      const result = await retryManager.executeWithRetry(
        async () => {
          // 动态导入 Firebase 模块
          const { onlineUserManager } = await import('@/lib/firebase')

          // 获取今日访客数和在线用户数
          const [todayVisitors, realTimeUsers] = await Promise.all([
            onlineUserManager.getTodayVisitors(),
            onlineUserManager.getOnlineCount()
          ])

          return { todayVisitors, realTimeUsers }
        },
        'firebase',
        { operationId: 'fetch_firebase_stats' }
      )

      // 使用智能算法计算在线用户数（Firebase有实时数据）
      const calculatedOnlineUsers = calculateOnlineUsers({
        todayVisitors: result.todayVisitors,
        source: 'firebase',
        realTimeUsers: result.realTimeUsers
      })

      const processedData = {
        success: true,
        todayVisitors: result.todayVisitors || 0,
        onlineUsers: calculatedOnlineUsers,
        realTimeUsers: result.realTimeUsers,
        source: 'firebase',
        timestamp: Date.now()
      }

      // 缓存数据（Firebase数据更新频繁，不使用持久化缓存）
      cacheManager.set(cacheKey, processedData, 'firebase', false)
      return processedData

    } catch (error) {
      console.warn('Firebase 获取失败:', error)
      setError(`Firebase: ${error.message}`)
      return { success: false }
    }
  }

  // 3. Vercel Analytics 数据获取
  const fetchVercelStats = async (forceRefresh = false) => {
    const cacheKey = 'vercel_stats'

    // 如果不是强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        return cached
      }
    }

    try {
      const result = await retryManager.executeWithRetry(
        async () => {
          const response = await fetch('/api/vercel-stats')
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
          return response.json()
        },
        'vercel',
        { operationId: 'fetch_vercel_stats' }
      )

      if (result.success !== false) {
        // 使用智能算法计算在线用户数
        const calculatedOnlineUsers = calculateOnlineUsers({
          todayVisitors: result.todayVisitors,
          totalPageViews: result.totalPageViews,
          source: 'vercel'
        })

        const processedData = {
          success: true,
          todayVisitors: result.todayVisitors || 0,
          onlineUsers: calculatedOnlineUsers,
          rawOnlineUsers: result.onlineUsers,
          source: result.source || 'vercel',
          note: result.note,
          timestamp: Date.now()
        }

        // 缓存数据 - 减少持久化缓存
        cacheManager.set(cacheKey, processedData, 'vercel', false)
        return processedData
      }
    } catch (error) {
      console.warn('Vercel Analytics 获取失败:', error)
      setError(`Vercel: ${error.message}`)
    }

    return { success: false }
  }

  // 4. 本地备用统计方案
  const getLocalStats = () => {
    const cacheKey = 'local_stats'

    // 尝试从缓存获取
    const cached = cacheManager.get(cacheKey, false) // 不检查持久化缓存
    if (cached) {
      return cached
    }

    const today = new Date().toDateString()
    const userId = getUserId()

    // 今日访问统计
    const todayKey = `visitors_${today}`
    let todayVisitors = JSON.parse(localStorage.getItem(todayKey) || '[]')

    if (!todayVisitors.includes(userId)) {
      todayVisitors.push(userId)
      localStorage.setItem(todayKey, JSON.stringify(todayVisitors))
    }

    // 使用智能算法计算在线用户数
    const calculatedOnlineUsers = calculateOnlineUsers({
      todayVisitors: todayVisitors.length,
      source: 'local'
    })

    const localData = {
      success: true,
      todayVisitors: todayVisitors.length,
      onlineUsers: calculatedOnlineUsers,
      source: 'local'
    }

    // 短时间缓存本地数据
    cacheManager.set(cacheKey, localData, 'local', false)
    return localData
  }

  // Firebase 会话管理
  const initFirebaseSession = async () => {
    try {
      const { onlineUserManager } = await import('@/lib/firebase')
      const userId = await onlineUserManager.initUserSession()
      if (userId) {
        localStorage.setItem('firebase_user_id', userId)
      }
    } catch (error) {
      console.warn('Firebase 会话初始化失败:', error)
    }
  }

  const updateFirebaseHeartbeat = async () => {
    try {
      const userId = localStorage.getItem('firebase_user_id')
      if (userId) {
        const { onlineUserManager } = await import('@/lib/firebase')
        await onlineUserManager.updateHeartbeat(userId)
      }
    } catch (error) {
      console.warn('Firebase 心跳更新失败:', error)
    }
  }

  const cleanupFirebaseSession = async () => {
    try {
      const userId = localStorage.getItem('firebase_user_id')
      if (userId) {
        const { onlineUserManager } = await import('@/lib/firebase')
        await onlineUserManager.removeUser(userId)
        localStorage.removeItem('firebase_user_id')
      }
    } catch (error) {
      console.warn('Firebase 会话清理失败:', error)
    }
  }

  // 启动 Firebase 自动清理
  const initFirebaseCleanup = async () => {
    try {
      const { cleanupManager } = await import('@/lib/firebase')
      cleanupManager.start()
    } catch (error) {
      console.warn('Firebase 自动清理启动失败:', error)
    }
  }

  // 停止 Firebase 自动清理
  const stopFirebaseCleanup = async () => {
    try {
      const { cleanupManager } = await import('@/lib/firebase')
      cleanupManager.stop()
    } catch (error) {
      console.warn('Firebase 自动清理停止失败:', error)
    }
  }

  // 获取或生成用户ID
  const getUserId = () => {
    let userId = localStorage.getItem('visitor_id')
    if (!userId) {
      userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
      localStorage.setItem('visitor_id', userId)
    }
    return userId
  }

  // 清理本地存储中的过期访客数据
  const cleanupLocalVisitorData = () => {
    try {
      const today = new Date().toDateString()
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toDateString()

      // 获取所有localStorage键
      const keys = Object.keys(localStorage)
      let cleanedCount = 0

      keys.forEach(key => {
        if (key.startsWith('visitors_')) {
          const dateStr = key.replace('visitors_', '')
          // 保留今天和昨天的数据，删除其他的
          if (dateStr !== today && dateStr !== yesterdayStr) {
            localStorage.removeItem(key)
            cleanedCount++
          }
        }
      })


    } catch (error) {
      console.warn('本地存储清理失败:', error)
    }
  }

  // 实时更新统计数据 - 改进的数据源选择策略
  const fetchRealTimeStats = async (forceRefresh = false) => {
    try {
      setError(null)

      // 并行获取所有可用数据源，避免串行等待
      const [umamiResult, firebaseResult, vercelResult] = await Promise.allSettled([
        fetchUmamiStats(forceRefresh),
        fetchFirebaseStats(forceRefresh),
        fetchVercelStats(forceRefresh)
      ])

      // 选择最佳数据源 - 优先选择成功且数据新鲜的源
      let bestData = null
      let dataAge = Infinity

      // 检查Umami数据
      if (umamiResult.status === 'fulfilled' && umamiResult.value.success) {
        const age = Date.now() - (umamiResult.value.timestamp || 0)
        if (age < dataAge) {
          bestData = umamiResult.value
          dataAge = age
        }
      }

      // 检查Firebase数据
      if (firebaseResult.status === 'fulfilled' && firebaseResult.value.success) {
        const age = Date.now() - (firebaseResult.value.timestamp || 0)
        if (age < dataAge) {
          bestData = firebaseResult.value
          dataAge = age
        }
      }

      // 检查Vercel数据
      if (vercelResult.status === 'fulfilled' && vercelResult.value.success) {
        const age = Date.now() - (vercelResult.value.timestamp || 0)
        if (age < dataAge) {
          bestData = vercelResult.value
          dataAge = age
        }
      }

      // 如果有可用数据，使用最新的
      if (bestData) {
        updateStats(bestData)
        return
      }

      // 所有远程数据源都失败，使用本地备用数据
      console.warn('所有远程数据源失败，使用本地备用数据')
      const localData = getLocalStats()
      updateStats(localData)

    } catch (error) {
      console.error('实时更新失败:', error)
      setError(`更新失败: ${error.message}`)

      // 发生错误时也使用本地备用数据
      const localData = getLocalStats()
      updateStats(localData)
    }
  }

  // 获取数据源显示文本和状态
  const getDataSourceInfo = () => {
    const health = healthStatus[dataSource]
    let icon = '⏳'
    let title = `数据源: ${dataSource}`

    // 处理混合数据源显示
    if (dataSource.includes('+')) {
      const sources = dataSource.split('+')
      const icons = sources.map(source => {
        switch (source) {
          case 'umami': return '📈'
          case 'firebase': return '🔥'
          case 'vercel': return '▲'
          case 'local': return '💾'
          default: return '❓'
        }
      })
      icon = icons.join('')
      title = `混合数据源: ${dataSource} (访客统计+在线用户)`
    } else {
      // 单一数据源
      switch (dataSource) {
        case 'umami':
          icon = '📈'
          break
        case 'firebase':
          icon = '🔥'
          break
        case 'vercel':
          icon = '▲'
          break
        case 'local':
          icon = '💾'
          break
        case 'loading':
          icon = '⏳'
          title = '加载中...'
          break
      }
    }

    // 添加健康状态指示
    if (health) {
      if (health.health === 'degraded') {
        icon += '⚠️'
        title += ` (服务降级)`
      } else if (health.health === 'unhealthy') {
        icon += '❌'
        title += ` (服务异常)`
      }
    }

    if (error) {
      title += ` - 错误: ${error}`
    }

    return { icon, title }
  }

  const { icon, title } = getDataSourceInfo()

  // 手动刷新数据
  const handleManualRefresh = async () => {
    if (!isLoading) {
      setIsLoading(true)
      try {
        await fetchRealTimeStats(true)
      } finally {
        setIsLoading(false)
      }
    }
  }

  return (
    <div className='flex items-center justify-center gap-1 text-xs'>
      <span
        className='flex items-center gap-1 cursor-pointer hover:opacity-80 transition-opacity duration-200'
        onClick={handleManualRefresh}
        title='点击刷新访问数据'
      >
        <span>📊今日访问人数:</span>
        <span className={`font-medium ${isLoading ? 'animate-pulse' : ''}`}>
          {isLoading ? '...' : todayVisitors}
        </span>
      </span>
      <span
        className='flex items-center gap-1 cursor-pointer hover:opacity-80 transition-opacity duration-200'
        onClick={handleManualRefresh}
        title='点击刷新在线数据'
      >
        <span>👨🏼‍🤝‍👨🏻当前在线人数:</span>
        <span className={`font-medium ${isLoading ? 'animate-pulse' : ''}`}>
          {isLoading ? '...' : onlineUsers}
        </span>
      </span>
      <span
        className={`text-xs opacity-60 cursor-help ${error ? 'text-red-400' : ''}`}
        title={title}
      >
        {icon}
      </span>
      {error && (
        <span className='text-xs text-red-400 opacity-80' title={error}>
          ⚠️
        </span>
      )}
    </div>
  )
}

export default VisitorStats
