/**
 * Vercel Analytics API 测试脚本
 * 验证Vercel Analytics配置是否正常工作
 */

const fetch = require('node-fetch')

// 从环境变量读取配置
const VERCEL_TEAM_ID = 'team_tMjAyby9e0CASn2c0wj37ier'
const VERCEL_PROJECT_ID = 'prj_aGs30RucQDkWKVoZdUOyxLopzY9q'
const VERCEL_ACCESS_TOKEN = '************************'

async function testVercelAnalyticsAPI() {
  console.log('🔍 测试Vercel Analytics API配置\n')
  console.log('=' * 60)

  // 1. 验证配置信息
  console.log('\n📋 配置信息验证:')
  console.log(`Team ID: ${VERCEL_TEAM_ID}`)
  console.log(`Project ID: ${VERCEL_PROJECT_ID}`)
  console.log(`Access Token: ${VERCEL_ACCESS_TOKEN.substring(0, 10)}...`)

  // 2. 测试API连接
  console.log('\n🔗 测试API连接...')
  
  try {
    // 获取时间范围
    const now = new Date()
    const today = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()))
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
    
    const since = today.getTime()
    const until = tomorrow.getTime()

    console.log(`时间范围: ${today.toISOString()} 到 ${tomorrow.toISOString()}`)
    console.log(`时间戳: ${since} 到 ${until}`)

    // 3. 测试不同的API端点
    const endpoints = [
      {
        name: 'Project Info',
        url: `https://api.vercel.com/v9/projects/${VERCEL_PROJECT_ID}?teamId=${VERCEL_TEAM_ID}`
      },
      {
        name: 'Web Analytics (新版)',
        url: `https://api.vercel.com/v1/projects/${VERCEL_PROJECT_ID}/analytics?teamId=${VERCEL_TEAM_ID}&from=${since}&to=${until}`
      },
      {
        name: 'Analytics Pageviews',
        url: `https://api.vercel.com/v1/projects/${VERCEL_PROJECT_ID}/analytics/pageviews?teamId=${VERCEL_TEAM_ID}&from=${since}&to=${until}`
      },
      {
        name: 'Analytics Visitors',
        url: `https://api.vercel.com/v1/projects/${VERCEL_PROJECT_ID}/analytics/visitors?teamId=${VERCEL_TEAM_ID}&from=${since}&to=${until}`
      },
      {
        name: 'Web Analytics Stats',
        url: `https://api.vercel.com/v1/projects/${VERCEL_PROJECT_ID}/web-analytics?teamId=${VERCEL_TEAM_ID}&from=${since}&to=${until}`
      }
    ]

    for (const endpoint of endpoints) {
      console.log(`\n🧪 测试 ${endpoint.name}:`)
      console.log(`URL: ${endpoint.url}`)
      
      try {
        const response = await fetch(endpoint.url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${VERCEL_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
          }
        })

        console.log(`状态码: ${response.status} ${response.statusText}`)
        
        if (response.ok) {
          const data = await response.json()
          console.log(`✅ 成功获取数据:`)
          console.log(JSON.stringify(data, null, 2))
        } else {
          const errorText = await response.text()
          console.log(`❌ 请求失败:`)
          console.log(errorText)
        }
      } catch (error) {
        console.log(`❌ 网络错误: ${error.message}`)
      }
    }

    // 4. 测试本地API端点
    console.log('\n\n🏠 测试本地API端点:')
    try {
      const localResponse = await fetch('http://localhost:3000/api/vercel-stats')
      console.log(`状态码: ${localResponse.status} ${localResponse.statusText}`)
      
      if (localResponse.ok) {
        const localData = await localResponse.json()
        console.log(`✅ 本地API响应:`)
        console.log(JSON.stringify(localData, null, 2))
      } else {
        const errorText = await localResponse.text()
        console.log(`❌ 本地API失败:`)
        console.log(errorText)
      }
    } catch (error) {
      console.log(`❌ 本地API错误: ${error.message}`)
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }

  console.log('\n' + '=' * 60)
  console.log('📊 测试完成')
}

// 运行测试
testVercelAnalyticsAPI().catch(console.error)
