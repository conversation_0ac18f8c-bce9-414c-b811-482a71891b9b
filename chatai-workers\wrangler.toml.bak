# Cloudflare Workers 配置文件
name = "chatai-workers"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 环境变量
[env.production.vars]
ENVIRONMENT = "production"

[env.development.vars]
ENVIRONMENT = "development"

# KV 存储 (用于聊天记录)
[[kv_namespaces]]
binding = "CHAT_STORAGE"
id = "6d6792bc55fb4e2a885d27f7ef1d6e74"
preview_id = "6d6792bc55fb4e2a885d27f7ef1d6e74"  # 如果你没有单独创建 preview 命名空间，使用同一个 ID

# R2 存储 (用于文件上传)
[[r2_buckets]]
binding = "FILE_STORAGE"
bucket_name = "r2-wobchatai"
preview_bucket_name = "r2-wobchatai"  # 如果没有额外创建 preview bucket，也统一使用同一个名称

# Durable Objects (用于实时聊天)
[[durable_objects.bindings]]
name = "CHAT_ROOM"
class_name = "ChatRoom"

[[migrations]]
tag = "v1"
new_classes = ["ChatRoom"]

# 路由配置
[triggers]
crons = ["0 0 * * *"]  # 每日清理任务

# 资源限制
[limits]
cpu_ms = 50000
