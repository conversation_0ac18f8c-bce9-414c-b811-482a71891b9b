/**
 * 在线用户数智能计算算法
 * 基于多种因素估算当前在线用户数
 */

/**
 * 智能计算在线用户数
 * @param {Object} options 计算参数
 * @param {number} options.todayVisitors 今日访客数
 * @param {number} options.totalPageViews 总页面浏览量
 * @param {number} options.avgSessionTime 平均会话时间(秒)
 * @param {string} options.source 数据源
 * @param {number} options.realTimeUsers 实时用户数(如果有)
 * @returns {number} 估算的在线用户数
 */
export function calculateOnlineUsers(options = {}) {
  const {
    todayVisitors = 0,
    totalPageViews = 0,
    avgSessionTime = 180, // 默认3分钟
    source = 'unknown',
    realTimeUsers = null
  } = options

  // 如果有实时用户数据，直接使用
  if (realTimeUsers !== null && realTimeUsers > 0) {
    return Math.max(1, realTimeUsers)
  }

  // 获取当前时间因子
  const timeFactors = getTimeFactors()
  
  // 基础算法：根据访客数和时间因子估算
  let estimatedUsers = 0

  if (source === 'umami' || source === 'vercel') {
    // 对于分析服务，使用更精确的算法
    estimatedUsers = calculateFromAnalytics({
      todayVisitors,
      totalPageViews,
      avgSessionTime,
      timeFactors
    })
  } else if (source === 'firebase') {
    // Firebase 有实时数据，使用不同算法
    estimatedUsers = calculateFromFirebase({
      todayVisitors,
      timeFactors
    })
  } else {
    // 本地存储等其他来源
    estimatedUsers = calculateBasic({
      todayVisitors,
      timeFactors
    })
  }

  // 确保最小值为1
  return Math.max(1, Math.round(estimatedUsers))
}

/**
 * 获取时间相关因子
 */
function getTimeFactors() {
  const now = new Date()
  const hour = now.getHours()
  const dayOfWeek = now.getDay() // 0=周日, 1=周一, ...
  const minute = now.getMinutes()

  // 一天中的活跃度因子 (0-1)
  const hourlyActivity = getHourlyActivityFactor(hour)
  
  // 一周中的活跃度因子 (0-1)
  const weeklyActivity = getWeeklyActivityFactor(dayOfWeek)
  
  // 实时波动因子 (基于分钟数)
  const realtimeFluctuation = 0.8 + (Math.sin(minute * Math.PI / 30) * 0.2)

  return {
    hourlyActivity,
    weeklyActivity,
    realtimeFluctuation,
    currentHour: hour,
    currentDay: dayOfWeek
  }
}

/**
 * 获取小时活跃度因子
 */
function getHourlyActivityFactor(hour) {
  // 基于一般网站访问模式的活跃度曲线
  const activityCurve = [
    0.1, 0.1, 0.1, 0.1, 0.1, 0.2, // 0-5点 (深夜)
    0.3, 0.4, 0.5, 0.7, 0.8, 0.9, // 6-11点 (上午)
    0.9, 0.8, 0.9, 1.0, 0.9, 0.8, // 12-17点 (下午)
    0.7, 0.8, 0.9, 0.7, 0.5, 0.3  // 18-23点 (晚上)
  ]
  
  return activityCurve[hour] || 0.5
}

/**
 * 获取星期活跃度因子
 */
function getWeeklyActivityFactor(dayOfWeek) {
  // 0=周日, 1=周一, ..., 6=周六
  const weeklyPattern = [0.6, 1.0, 1.0, 1.0, 1.0, 1.0, 0.8]
  return weeklyPattern[dayOfWeek] || 0.8
}

/**
 * 基于分析服务数据计算
 */
function calculateFromAnalytics({ todayVisitors, totalPageViews, avgSessionTime, timeFactors }) {
  const { hourlyActivity, weeklyActivity, realtimeFluctuation } = timeFactors
  
  // 基础在线率 (考虑会话时间)
  const sessionMinutes = avgSessionTime / 60
  const baseOnlineRate = Math.min(0.3, sessionMinutes / 20) // 最大30%
  
  // 页面浏览深度因子
  const pageDepthFactor = totalPageViews > 0 ? Math.min(2, totalPageViews / todayVisitors) : 1
  
  // 综合计算
  const estimatedUsers = todayVisitors * 
    baseOnlineRate * 
    hourlyActivity * 
    weeklyActivity * 
    realtimeFluctuation * 
    pageDepthFactor

  return estimatedUsers
}

/**
 * 基于Firebase实时数据计算
 */
function calculateFromFirebase({ todayVisitors, timeFactors }) {
  const { hourlyActivity, weeklyActivity, realtimeFluctuation } = timeFactors
  
  // Firebase 数据更准确，使用较高的基础在线率
  const baseOnlineRate = 0.25
  
  const estimatedUsers = todayVisitors * 
    baseOnlineRate * 
    hourlyActivity * 
    weeklyActivity * 
    realtimeFluctuation

  return estimatedUsers
}

/**
 * 基础计算方法
 */
function calculateBasic({ todayVisitors, timeFactors }) {
  const { hourlyActivity, weeklyActivity, realtimeFluctuation } = timeFactors
  
  // 保守的基础在线率
  const baseOnlineRate = 0.15
  
  const estimatedUsers = todayVisitors * 
    baseOnlineRate * 
    hourlyActivity * 
    weeklyActivity * 
    realtimeFluctuation

  return estimatedUsers
}

/**
 * 获取在线用户数的置信区间
 */
export function getOnlineUsersRange(baseEstimate) {
  const variance = Math.max(1, baseEstimate * 0.3)
  const min = Math.max(1, Math.floor(baseEstimate - variance))
  const max = Math.ceil(baseEstimate + variance)
  
  return { min, max, estimate: baseEstimate }
}

/**
 * 平滑在线用户数变化 (避免剧烈波动)
 */
export function smoothOnlineUsers(newValue, previousValue, smoothingFactor = 0.3) {
  if (previousValue === null || previousValue === undefined) {
    return newValue
  }
  
  // 使用指数移动平均
  return Math.round(previousValue * (1 - smoothingFactor) + newValue * smoothingFactor)
}
