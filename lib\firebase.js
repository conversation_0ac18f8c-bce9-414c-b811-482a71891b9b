/**
 * Firebase 配置文件
 * 用于实时在线用户统计
 */

import { initializeApp } from 'firebase/app'
import { getDatabase, ref, set, remove, onValue, serverTimestamp, onDisconnect } from 'firebase/database'

// Firebase 配置
const firebaseConfig = {
  apiKey: "AIzaSyDrqTB4l3lBI6U_z816U4S2ZMN8vkklvZI",
  authDomain: "analytics-aad8a.firebaseapp.com",
  databaseURL: "https://analytics-aad8a-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "analytics-aad8a",
  storageBucket: "analytics-aad8a.appspot.com",
  messagingSenderId: "900332113616",
  appId: "1:900332113616:web:analytics-aad8a" // 临时使用项目ID作为appId
}

// 初始化 Firebase
const app = initializeApp(firebaseConfig)
export const database = getDatabase(app)

// 生成用户ID
const generateUserId = () => {
  return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 获取今日日期字符串
const getTodayKey = () => {
  return new Date().toISOString().split('T')[0] // YYYY-MM-DD 格式
}

/**
 * 在线用户管理函数
 */
export const onlineUserManager = {
  // 添加用户到在线列表
  addUser: async (userId) => {
    if (!database) return false

    try {
      const userRef = ref(database, `online_users/${userId}`)
      await set(userRef, {
        timestamp: serverTimestamp(),
        lastSeen: Date.now()
      })

      // 设置断线时自动移除
      onDisconnect(userRef).remove()

      return true
    } catch (error) {
      console.error('Firebase 添加用户失败:', error)
      return false
    }
  },

  // 移除用户从在线列表
  removeUser: async (userId) => {
    if (!database) return false

    try {
      const userRef = ref(database, `online_users/${userId}`)
      await remove(userRef)
      return true
    } catch (error) {
      console.error('Firebase 移除用户失败:', error)
      return false
    }
  },

  // 获取在线用户数量
  getOnlineCount: async () => {
    if (!database) return 0

    try {
      return new Promise((resolve) => {
        const onlineRef = ref(database, 'online_users')
        onValue(onlineRef, (snapshot) => {
          const users = snapshot.val()
          if (!users) {
            resolve(0)
            return
          }

          // 清理超时用户（5分钟无活动）
          const now = Date.now()
          const timeout = 5 * 60 * 1000 // 5分钟
          let activeCount = 0

          Object.keys(users).forEach(userId => {
            const user = users[userId]
            if (user.lastSeen && (now - user.lastSeen) < timeout) {
              activeCount++
            } else {
              // 清理超时用户
              const userRef = ref(database, `online_users/${userId}`)
              remove(userRef)
            }
          })

          resolve(Math.max(1, activeCount))
        }, { onlyOnce: true })
      })
    } catch (error) {
      console.error('Firebase 获取在线用户数失败:', error)
      return 1
    }
  },

  // 添加今日访客
  addTodayVisitor: async (userId) => {
    if (!database) return false

    try {
      const today = getTodayKey()
      const visitorRef = ref(database, `daily_visitors/${today}/${userId}`)
      await set(visitorRef, {
        timestamp: serverTimestamp(),
        firstVisit: Date.now()
      })
      return true
    } catch (error) {
      console.error('Firebase 添加今日访客失败:', error)
      return false
    }
  },

  // 获取今日访问用户数
  getTodayVisitors: async () => {
    if (!database) return 0

    try {
      return new Promise((resolve) => {
        const today = getTodayKey()
        const visitorsRef = ref(database, `daily_visitors/${today}`)
        onValue(visitorsRef, (snapshot) => {
          const visitors = snapshot.val()
          const count = visitors ? Object.keys(visitors).length : 0
          resolve(count)
        }, { onlyOnce: true })
      })
    } catch (error) {
      console.error('Firebase 获取今日访客数失败:', error)
      return 0
    }
  },

  // 初始化用户会话
  initUserSession: async () => {
    if (!database) return null

    try {
      const userId = generateUserId()

      // 添加到在线用户
      await onlineUserManager.addUser(userId)

      // 添加到今日访客
      await onlineUserManager.addTodayVisitor(userId)

      return userId
    } catch (error) {
      console.error('Firebase 初始化用户会话失败:', error)
      return null
    }
  },

  // 更新用户心跳
  updateHeartbeat: async (userId) => {
    if (!database || !userId) return false

    try {
      const userRef = ref(database, `online_users/${userId}`)
      await set(userRef, {
        timestamp: serverTimestamp(),
        lastSeen: Date.now()
      })
      return true
    } catch (error) {
      console.error('Firebase 更新心跳失败:', error)
      return false
    }
  },

  // 清理过期的每日访客数据
  cleanupOldVisitorData: async () => {
    if (!database) return false

    try {
      const visitorsRef = ref(database, 'daily_visitors')
      const snapshot = await new Promise((resolve, reject) => {
        onValue(visitorsRef, resolve, { onlyOnce: true })
      })

      const allData = snapshot.val()
      if (!allData) return true

      const today = getTodayKey()
      const yesterday = getYesterdayKey()
      let deletedCount = 0

      // 删除除今天和昨天之外的所有数据
      for (const dateKey of Object.keys(allData)) {
        if (dateKey !== today && dateKey !== yesterday) {
          const dateRef = ref(database, `daily_visitors/${dateKey}`)
          await remove(dateRef)
          deletedCount++
          console.log(`已清理过期访客数据: ${dateKey}`)
        }
      }

      if (deletedCount > 0) {
        console.log(`Firebase 清理完成: 删除了 ${deletedCount} 天的过期数据`)
      }

      return true
    } catch (error) {
      console.error('Firebase 清理过期数据失败:', error)
      return false
    }
  },

  // 重置今日访客数据（仅在需要时使用）
  resetTodayVisitors: async () => {
    if (!database) return false

    try {
      const today = getTodayKey()
      const todayRef = ref(database, `daily_visitors/${today}`)
      await remove(todayRef)
      console.log(`已重置今日访客数据: ${today}`)
      return true
    } catch (error) {
      console.error('Firebase 重置今日数据失败:', error)
      return false
    }
  }
}

// 获取昨日日期字符串
const getYesterdayKey = () => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return yesterday.toISOString().split('T')[0] // YYYY-MM-DD 格式
}

/**
 * 自动清理管理器
 * 定期清理过期数据
 */
export const cleanupManager = {
  cleanupInterval: null,
  isRunning: false,

  // 启动自动清理
  start: () => {
    if (cleanupManager.isRunning) return

    cleanupManager.isRunning = true

    // 立即执行一次清理
    cleanupManager.performCleanup()

    // 每6小时执行一次清理
    cleanupManager.cleanupInterval = setInterval(() => {
      cleanupManager.performCleanup()
    }, 6 * 60 * 60 * 1000) // 6小时

    console.log('Firebase 自动清理已启动')
  },

  // 停止自动清理
  stop: () => {
    if (cleanupManager.cleanupInterval) {
      clearInterval(cleanupManager.cleanupInterval)
      cleanupManager.cleanupInterval = null
    }
    cleanupManager.isRunning = false
    console.log('Firebase 自动清理已停止')
  },

  // 执行清理操作
  performCleanup: async () => {
    try {
      console.log('开始执行 Firebase 数据清理...')

      // 清理过期访客数据
      await onlineUserManager.cleanupOldVisitorData()

      // 清理超时的在线用户（这个在getOnlineCount中已经有了，但这里再执行一次确保清理）
      const onlineRef = ref(database, 'online_users')
      const snapshot = await new Promise((resolve) => {
        onValue(onlineRef, resolve, { onlyOnce: true })
      })

      const users = snapshot.val()
      if (users) {
        const now = Date.now()
        const timeout = 10 * 60 * 1000 // 10分钟超时（比正常的5分钟更宽松）
        let cleanedCount = 0

        for (const userId of Object.keys(users)) {
          const user = users[userId]
          if (user.lastSeen && (now - user.lastSeen) > timeout) {
            const userRef = ref(database, `online_users/${userId}`)
            await remove(userRef)
            cleanedCount++
          }
        }

        if (cleanedCount > 0) {
          console.log(`清理了 ${cleanedCount} 个超时的在线用户`)
        }
      }

      console.log('Firebase 数据清理完成')
    } catch (error) {
      console.error('Firebase 数据清理失败:', error)
    }
  }
}
