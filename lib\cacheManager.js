/**
 * 数据缓存管理器
 * 提供内存缓存和本地存储缓存功能
 */

// 内存缓存
const memoryCache = new Map()

// 缓存配置 - 禁用缓存以获取真实数据
const CACHE_CONFIG = {
  // 不同数据类型的缓存时间 (毫秒) - 设置为0禁用缓存
  TTL: {
    umami: 0,                  // 禁用缓存 - 始终获取真实数据
    firebase: 0,               // 禁用缓存 - 始终获取真实数据
    vercel: 0,                 // 禁用缓存 - 始终获取真实数据
    local: 0,                  // 禁用缓存 - 始终获取真实数据
    default: 0                 // 禁用缓存 - 始终获取真实数据
  },

  // 最大缓存条目数
  MAX_ENTRIES: 100,

  // 本地存储前缀
  STORAGE_PREFIX: 'visitor_cache_'
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  constructor() {
    this.memoryCache = memoryCache
    this.cleanupInterval = null
    this.startCleanup()
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {string} type 数据类型 (umami, firebase, vercel, local)
   * @param {boolean} persistent 是否持久化到本地存储
   */
  set(key, value, type = 'default', persistent = false) {
    const ttl = CACHE_CONFIG.TTL[type] || CACHE_CONFIG.TTL.default
    const expiresAt = Date.now() + ttl
    
    const cacheItem = {
      value,
      type,
      expiresAt,
      createdAt: Date.now()
    }

    // 内存缓存
    this.memoryCache.set(key, cacheItem)
    
    // 持久化缓存
    if (persistent && typeof window !== 'undefined') {
      try {
        const storageKey = CACHE_CONFIG.STORAGE_PREFIX + key
        localStorage.setItem(storageKey, JSON.stringify(cacheItem))
      } catch (error) {
        console.warn('本地存储缓存失败:', error)
      }
    }

    // 限制缓存大小
    this.limitCacheSize()
    
    console.log(`缓存设置: ${key} (${type}, TTL: ${ttl}ms)`)
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {boolean} checkPersistent 是否检查本地存储
   * @returns {any|null} 缓存值或null
   */
  get(key, checkPersistent = true) {
    // 先检查内存缓存
    let cacheItem = this.memoryCache.get(key)
    
    // 如果内存中没有，检查本地存储
    if (!cacheItem && checkPersistent && typeof window !== 'undefined') {
      try {
        const storageKey = CACHE_CONFIG.STORAGE_PREFIX + key
        const stored = localStorage.getItem(storageKey)
        if (stored) {
          cacheItem = JSON.parse(stored)
          // 恢复到内存缓存
          this.memoryCache.set(key, cacheItem)
        }
      } catch (error) {
        console.warn('本地存储读取失败:', error)
      }
    }

    // 检查是否过期
    if (cacheItem) {
      if (Date.now() > cacheItem.expiresAt) {
        this.delete(key)
        console.log(`缓存过期: ${key}`)
        return null
      }
      
      console.log(`缓存命中: ${key} (age: ${Date.now() - cacheItem.createdAt}ms)`)
      return cacheItem.value
    }

    return null
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.memoryCache.delete(key)
    
    if (typeof window !== 'undefined') {
      try {
        const storageKey = CACHE_CONFIG.STORAGE_PREFIX + key
        localStorage.removeItem(storageKey)
      } catch (error) {
        console.warn('本地存储删除失败:', error)
      }
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear()
    
    if (typeof window !== 'undefined') {
      try {
        const keys = Object.keys(localStorage)
        keys.forEach(key => {
          if (key.startsWith(CACHE_CONFIG.STORAGE_PREFIX)) {
            localStorage.removeItem(key)
          }
        })
      } catch (error) {
        console.warn('本地存储清空失败:', error)
      }
    }
    
    console.log('所有缓存已清空')
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const memorySize = this.memoryCache.size
    let persistentSize = 0
    
    if (typeof window !== 'undefined') {
      try {
        const keys = Object.keys(localStorage)
        persistentSize = keys.filter(key => 
          key.startsWith(CACHE_CONFIG.STORAGE_PREFIX)
        ).length
      } catch (error) {
        console.warn('获取本地存储统计失败:', error)
      }
    }

    return {
      memorySize,
      persistentSize,
      maxEntries: CACHE_CONFIG.MAX_ENTRIES
    }
  }

  /**
   * 限制缓存大小
   */
  limitCacheSize() {
    if (this.memoryCache.size > CACHE_CONFIG.MAX_ENTRIES) {
      // 删除最旧的条目
      const entries = Array.from(this.memoryCache.entries())
      entries.sort((a, b) => a[1].createdAt - b[1].createdAt)
      
      const toDelete = entries.slice(0, entries.length - CACHE_CONFIG.MAX_ENTRIES)
      toDelete.forEach(([key]) => {
        this.memoryCache.delete(key)
      })
      
      console.log(`缓存大小限制: 删除了 ${toDelete.length} 个旧条目`)
    }
  }

  /**
   * 启动定期清理
   */
  startCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // 每5分钟清理一次
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    let deletedCount = 0
    
    // 清理内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expiresAt) {
        this.memoryCache.delete(key)
        deletedCount++
      }
    }
    
    // 清理本地存储缓存
    if (typeof window !== 'undefined') {
      try {
        const keys = Object.keys(localStorage)
        keys.forEach(storageKey => {
          if (storageKey.startsWith(CACHE_CONFIG.STORAGE_PREFIX)) {
            try {
              const item = JSON.parse(localStorage.getItem(storageKey))
              if (item && now > item.expiresAt) {
                localStorage.removeItem(storageKey)
                deletedCount++
              }
            } catch (error) {
              // 删除损坏的缓存项
              localStorage.removeItem(storageKey)
              deletedCount++
            }
          }
        })
      } catch (error) {
        console.warn('本地存储清理失败:', error)
      }
    }
    
    if (deletedCount > 0) {
      console.log(`缓存清理: 删除了 ${deletedCount} 个过期条目`)
    }
  }

  /**
   * 停止清理
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager()

/**
 * 缓存装饰器函数
 * @param {string} keyPrefix 缓存键前缀
 * @param {string} type 数据类型
 * @param {boolean} persistent 是否持久化
 */
export function withCache(keyPrefix, type = 'default', persistent = false) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value
    
    descriptor.value = async function(...args) {
      const cacheKey = `${keyPrefix}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cached = cacheManager.get(cacheKey)
      if (cached !== null) {
        return cached
      }
      
      // 执行原方法
      const result = await method.apply(this, args)
      
      // 缓存结果
      if (result !== null && result !== undefined) {
        cacheManager.set(cacheKey, result, type, persistent)
      }
      
      return result
    }
    
    return descriptor
  }
}
