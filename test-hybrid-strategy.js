/**
 * 混合策略测试脚本
 * 验证访客统计（Umami/Vercel）+ 在线用户数（Firebase）的混合策略
 */

const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testAPI(endpoint) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`)
    const data = await response.json()
    return { success: response.ok, data }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function testHybridStrategy() {
  console.log('🧪 测试混合策略实现\n')
  console.log('=' * 60)

  // 1. 测试各个数据源
  console.log('\n📊 测试各个数据源:')
  
  const umami = await testAPI('/api/umami-stats')
  console.log('\n🔹 Umami Analytics:')
  if (umami.success) {
    console.log(`  ✅ 状态: 正常`)
    console.log(`  📈 今日访客: ${umami.data.todayVisitors}`)
    console.log(`  👥 在线用户: ${umami.data.onlineUsers} (估算)`)
    console.log(`  📄 页面浏览: ${umami.data.totalPageViews || 'N/A'}`)
  } else {
    console.log(`  ❌ 状态: 失败 - ${umami.error}`)
  }

  const firebase = await testAPI('/api/firebase-test')
  console.log('\n🔹 Firebase Realtime:')
  if (firebase.success) {
    console.log(`  ✅ 状态: 正常`)
    console.log(`  📈 今日访客: ${firebase.data.todayVisitors}`)
    console.log(`  👥 在线用户: ${firebase.data.onlineUsers} (实时检测)`)
  } else {
    console.log(`  ❌ 状态: 失败 - ${firebase.error}`)
  }

  const vercel = await testAPI('/api/vercel-stats')
  console.log('\n🔹 Vercel Analytics:')
  if (vercel.success) {
    console.log(`  ✅ 状态: 正常`)
    console.log(`  📈 今日访客: ${vercel.data.todayVisitors}`)
    console.log(`  👥 在线用户: ${vercel.data.onlineUsers} (估算)`)
    console.log(`  📝 备注: ${vercel.data.note || 'N/A'}`)
  } else {
    console.log(`  ❌ 状态: 失败 - ${vercel.error}`)
  }

  // 2. 分析混合策略
  console.log('\n\n🎯 混合策略分析:')
  console.log('=' * 40)

  let bestVisitorSource = null
  let bestOnlineSource = null

  // 选择最佳访客数据源（优先Umami/Vercel）
  if (umami.success) {
    bestVisitorSource = { name: 'Umami', data: umami.data }
  } else if (vercel.success) {
    bestVisitorSource = { name: 'Vercel', data: vercel.data }
  } else if (firebase.success) {
    bestVisitorSource = { name: 'Firebase', data: firebase.data }
  }

  // 选择最佳在线用户数据源（优先Firebase）
  if (firebase.success) {
    bestOnlineSource = { name: 'Firebase', data: firebase.data }
  } else if (umami.success) {
    bestOnlineSource = { name: 'Umami', data: umami.data }
  } else if (vercel.success) {
    bestOnlineSource = { name: 'Vercel', data: vercel.data }
  }

  console.log('\n📊 最佳访客数据源:')
  if (bestVisitorSource) {
    console.log(`  🏆 选择: ${bestVisitorSource.name}`)
    console.log(`  📈 今日访客: ${bestVisitorSource.data.todayVisitors}`)
    console.log(`  ✅ 原因: ${bestVisitorSource.name === 'Umami' || bestVisitorSource.name === 'Vercel' ? '专业分析服务，数据更准确' : '备用数据源'}`)
  } else {
    console.log(`  ❌ 无可用数据源`)
  }

  console.log('\n👥 最佳在线用户数据源:')
  if (bestOnlineSource) {
    console.log(`  🏆 选择: ${bestOnlineSource.name}`)
    console.log(`  👥 在线用户: ${bestOnlineSource.data.onlineUsers}`)
    console.log(`  ✅ 原因: ${bestOnlineSource.name === 'Firebase' ? '实时检测，数据真实' : '智能估算算法'}`)
  } else {
    console.log(`  ❌ 无可用数据源`)
  }

  // 3. 模拟混合结果
  console.log('\n\n🔄 混合策略结果:')
  console.log('=' * 40)

  if (bestVisitorSource || bestOnlineSource) {
    const hybridResult = {
      todayVisitors: bestVisitorSource?.data.todayVisitors || bestOnlineSource?.data.todayVisitors || 0,
      onlineUsers: bestOnlineSource?.data.onlineUsers || bestVisitorSource?.data.onlineUsers || 1,
      visitorSource: bestVisitorSource?.name || 'local',
      onlineSource: bestOnlineSource?.name || 'estimated',
      isRealTimeOnline: bestOnlineSource?.name === 'Firebase'
    }

    console.log(`📈 最终今日访客数: ${hybridResult.todayVisitors} (来源: ${hybridResult.visitorSource})`)
    console.log(`👥 最终在线用户数: ${hybridResult.onlineUsers} (来源: ${hybridResult.onlineSource})`)
    console.log(`🔴 在线用户检测类型: ${hybridResult.isRealTimeOnline ? '实时检测' : '智能估算'}`)
    console.log(`📊 数据源组合: ${hybridResult.visitorSource}+${hybridResult.onlineSource}`)

    // 4. 验证实现状态
    console.log('\n\n✅ 实现状态验证:')
    console.log('=' * 40)
    
    const hasAccurateVisitors = (bestVisitorSource?.name === 'Umami' || bestVisitorSource?.name === 'Vercel')
    const hasRealTimeOnline = (bestOnlineSource?.name === 'Firebase')
    
    console.log(`✅ 准确的访客统计 (Umami/Vercel): ${hasAccurateVisitors ? '✅ 已实现' : '❌ 未实现'}`)
    console.log(`✅ 真实的在线用户数 (Firebase): ${hasRealTimeOnline ? '✅ 已实现' : '❌ 未实现'}`)
    
    if (hasAccurateVisitors && hasRealTimeOnline) {
      console.log('\n🎉 混合策略完美实现！')
      console.log('   📊 访客统计来自专业分析服务')
      console.log('   👥 在线用户数来自实时检测')
    } else if (hasAccurateVisitors) {
      console.log('\n⚠️  部分实现：访客统计准确，但在线用户数为估算')
    } else if (hasRealTimeOnline) {
      console.log('\n⚠️  部分实现：在线用户数真实，但访客统计来自备用源')
    } else {
      console.log('\n❌ 混合策略未完全实现，使用备用数据')
    }

  } else {
    console.log('❌ 所有数据源都不可用')
  }

  console.log('\n' + '=' * 60)
}

// 运行测试
testHybridStrategy().catch(console.error)
