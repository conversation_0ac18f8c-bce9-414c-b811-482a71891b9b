/**
 * 资源加载管理器
 * 实现本地优先、CDN备用的资源加载策略
 */

// 资源映射表：本地路径 -> CDN备用地址
const RESOURCE_MAP = {
  // APlayer 音乐播放器
  '/js/aplayer.min.js': [
    'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js',
    'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.js'
  ],
  '/css/aplayer.min.css': [
    'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css',
    'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.css'
  ],
  
  // MetingJS
  '/js/meting.min.js': [
    'https://unpkg.com/meting@2.0.1/dist/Meting.min.js',
    'https://cdn.jsdelivr.net/npm/meting@2.0.1/dist/Meting.min.js'
  ],
  
  // Animate.css
  '/css/animate.min.css': [
    'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
    'https://unpkg.com/animate.css@4.1.1/animate.min.css'
  ],
  
  // Mermaid 图表
  '/js/mermaid.min.js': [
    'https://cdnjs.cloudflare.com/ajax/libs/mermaid/11.4.0/mermaid.min.js',
    'https://unpkg.com/mermaid@11.4.0/dist/mermaid.min.js'
  ],
  
  // VConsole
  '/js/vconsole.min.js': [
    'https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js',
    'https://unpkg.com/vconsole@3.3.4/dist/vconsole.min.js'
  ],
  
  // Gitalk 评论系统
  '/js/gitalk.min.js': [
    'https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.min.js',
    'https://unpkg.com/gitalk@1/dist/gitalk.min.js'
  ],
  '/css/gitalk.css': [
    'https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.css',
    'https://unpkg.com/gitalk@1/dist/gitalk.css'
  ],
  
  // Valine 评论系统
  '/js/valine.min.js': [
    'https://unpkg.com/valine@1.5.1/dist/Valine.min.js',
    'https://cdn.jsdelivr.net/npm/valine@1.5.1/dist/Valine.min.js'
  ],
  
  // WebWhiz AI组件
  '/js/webwhiz-sdk.js': [
    'https://www.unpkg.com/webwhiz@1.0.0/dist/sdk.js',
    'https://unpkg.com/webwhiz@1.0.0/dist/sdk.js'
  ]
};

/**
 * 检查资源是否可用
 * @param {string} url 资源URL
 * @returns {Promise<boolean>}
 */
async function checkResourceAvailable(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * 加载外部资源（增强版）
 * 优先尝试本地资源，失败时自动回退到CDN
 * @param {string} localPath 本地资源路径
 * @param {string} type 资源类型 'js' | 'css'
 * @returns {Promise<string>} 成功加载的资源URL
 */
export async function loadResourceWithFallback(localPath, type = 'js') {
  // 检查是否已存在
  const existingElements = type === 'js'
    ? document.querySelectorAll(`script[src="${localPath}"]`)
    : document.querySelectorAll(`link[href="${localPath}"]`);
  
  if (existingElements.length > 0) {
    return localPath;
  }

  // 首先尝试加载本地资源
  try {
    await loadExternalResource(localPath, type);
    console.log(`✅ 本地资源加载成功: ${localPath}`);
    return localPath;
  } catch (error) {
    console.warn(`⚠️ 本地资源加载失败: ${localPath}，尝试CDN备用`);
    
    // 获取CDN备用地址列表
    const fallbackUrls = RESOURCE_MAP[localPath] || [];
    
    // 依次尝试CDN备用地址
    for (const cdnUrl of fallbackUrls) {
      try {
        await loadExternalResource(cdnUrl, type);
        console.log(`✅ CDN备用资源加载成功: ${cdnUrl}`);
        return cdnUrl;
      } catch (cdnError) {
        console.warn(`⚠️ CDN备用资源加载失败: ${cdnUrl}`);
      }
    }
    
    // 所有资源都加载失败
    throw new Error(`所有资源加载失败: ${localPath}`);
  }
}

/**
 * 原始的loadExternalResource函数（从utils/index.js复制）
 */
function loadExternalResource(url, type = 'js') {
  return new Promise((resolve, reject) => {
    // 检查是否已存在
    const elements = type === 'js'
      ? document.querySelectorAll(`script[src='${url}']`)
      : document.querySelectorAll(`link[href='${url}']`);

    if (elements.length > 0 || !url) {
      resolve(url);
      return;
    }

    let tag;

    if (type === 'css') {
      tag = document.createElement('link');
      tag.rel = 'stylesheet';
      tag.href = url;
    } else if (type === 'font') {
      tag = document.createElement('link');
      tag.rel = 'preload';
      tag.as = 'font';
      tag.href = url;
    } else if (type === 'js') {
      tag = document.createElement('script');
      tag.src = url;
    }

    if (tag) {
      tag.onload = () => resolve(url);
      tag.onerror = () => reject(new Error(`Failed to load ${type}: ${url}`));
      document.head.appendChild(tag);
    } else {
      reject(new Error(`Unsupported resource type: ${type}`));
    }
  });
}

/**
 * 批量加载资源
 * @param {Array} resources 资源列表 [{path: string, type: string}]
 * @returns {Promise<Array>} 加载结果
 */
export async function loadMultipleResources(resources) {
  const results = [];
  
  for (const resource of resources) {
    try {
      const loadedUrl = await loadResourceWithFallback(resource.path, resource.type);
      results.push({ success: true, url: loadedUrl, resource });
    } catch (error) {
      results.push({ success: false, error: error.message, resource });
    }
  }
  
  return results;
}

/**
 * 获取资源的所有可用地址（本地+CDN）
 * @param {string} localPath 本地路径
 * @returns {Array<string>} 所有可用地址
 */
export function getResourceUrls(localPath) {
  const urls = [localPath];
  const fallbackUrls = RESOURCE_MAP[localPath] || [];
  return urls.concat(fallbackUrls);
}

export { RESOURCE_MAP };
