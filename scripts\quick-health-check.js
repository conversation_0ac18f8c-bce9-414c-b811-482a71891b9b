/**
 * 快速API健康检查脚本
 * 用于日常监控API状态
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// 关键API端点
const CRITICAL_APIS = [
  { name: 'Firebase数据', path: '/api/firebase-test' },
  { name: 'Umami统计', path: '/api/umami-stats' },
  { name: '缓存管理', path: '/api/cache' }
];

/**
 * 快速健康检查
 */
async function quickHealthCheck() {
  console.log('🏥 API快速健康检查');
  console.log('=' * 30);
  
  let healthyCount = 0;
  const results = [];
  
  for (const api of CRITICAL_APIS) {
    try {
      const startTime = Date.now();
      const response = await fetch(`${BASE_URL}${api.path}`);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const isHealthy = response.ok;
      if (isHealthy) healthyCount++;
      
      const status = isHealthy ? '✅' : '❌';
      console.log(`${status} ${api.name}: ${response.status} (${responseTime}ms)`);
      
      results.push({
        name: api.name,
        healthy: isHealthy,
        status: response.status,
        responseTime
      });
      
    } catch (error) {
      console.log(`💥 ${api.name}: 连接失败`);
      results.push({
        name: api.name,
        healthy: false,
        error: error.message
      });
    }
  }
  
  console.log('\n📊 健康状态总结:');
  console.log(`健康API: ${healthyCount}/${CRITICAL_APIS.length}`);
  console.log(`健康率: ${(healthyCount/CRITICAL_APIS.length*100).toFixed(1)}%`);
  
  if (healthyCount === CRITICAL_APIS.length) {
    console.log('🎉 所有关键API运行正常！');
  } else {
    console.log('⚠️  部分API需要关注');
  }
  
  return results;
}

// 运行检查
if (require.main === module) {
  quickHealthCheck().catch(console.error);
}

module.exports = { quickHealthCheck };
