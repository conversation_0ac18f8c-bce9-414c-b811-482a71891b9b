#!/usr/bin/env node

/**
 * AI密钥管理工具
 * 用于批量设置和管理多个AI平台的API密钥
 */

const { execSync } = require('child_process')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// AI平台配置
const AI_PROVIDERS = {
  // 国外平台
  openai: {
    name: 'OpenAI GPT',
    env: 'OPENAI_API_KEYS',
    description: 'OpenAI API密钥 (支持GPT-4, GPT-3.5等)',
    example: 'sk-xxx,sk-yyy,sk-zzz'
  },
  anthropic: {
    name: 'Anthropic Claude',
    env: 'ANTHROPIC_API_KEYS', 
    description: 'Anthropic API密钥 (支持Claude-3.5等)',
    example: 'sk-ant-xxx,sk-ant-yyy'
  },
  gemini: {
    name: 'Google Gemini',
    env: 'GEMINI_API_KEYS',
    description: 'Google Gemini API密钥 (免费额度大)',
    example: 'AIzaSyAXvL...,AIzaSyDj-s...'
  },
  
  // 国内平台
  qwen: {
    name: '阿里通义千问',
    env: 'QWEN_API_KEYS',
    description: '阿里云通义千问API密钥',
    example: 'sk-xxx,sk-yyy'
  },
  baidu: {
    name: '百度文心一言',
    env: 'BAIDU_API_KEYS',
    description: '百度文心一言API密钥 (格式: client_id:client_secret)',
    example: 'client_id1:secret1,client_id2:secret2'
  },
  zhipu: {
    name: '智谱AI',
    env: 'ZHIPU_API_KEYS',
    description: '智谱AI GLM API密钥',
    example: 'xxx.yyy,aaa.bbb'
  },
  moonshot: {
    name: '月之暗面 Kimi',
    env: 'MOONSHOT_API_KEYS',
    description: '月之暗面 Moonshot API密钥',
    example: 'sk-xxx,sk-yyy'
  },
  deepseek: {
    name: 'DeepSeek',
    env: 'DEEPSEEK_API_KEYS',
    description: 'DeepSeek API密钥 (代码能力强)',
    example: 'sk-xxx,sk-yyy'
  },
  minimax: {
    name: 'MiniMax',
    env: 'MINIMAX_API_KEYS',
    description: 'MiniMax API密钥',
    example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...'
  },

  // 更多国外平台
  cohere: {
    name: 'Cohere',
    env: 'COHERE_API_KEYS',
    description: 'Cohere API密钥',
    example: 'co_xxx,co_yyy'
  },
  mistral: {
    name: 'Mistral AI',
    env: 'MISTRAL_API_KEYS',
    description: 'Mistral AI API密钥',
    example: 'xxx,yyy'
  },
  perplexity: {
    name: 'Perplexity',
    env: 'PERPLEXITY_API_KEYS',
    description: 'Perplexity API密钥',
    example: 'pplx-xxx,pplx-yyy'
  },
  groq: {
    name: 'Groq',
    env: 'GROQ_API_KEYS',
    description: 'Groq API密钥 (免费额度)',
    example: 'gsk_xxx,gsk_yyy'
  },
  together: {
    name: 'Together AI',
    env: 'TOGETHER_API_KEYS',
    description: 'Together AI API密钥',
    example: 'xxx,yyy'
  },
  fireworks: {
    name: 'Fireworks AI',
    env: 'FIREWORKS_API_KEYS',
    description: 'Fireworks AI API密钥',
    example: 'fw_xxx,fw_yyy'
  },
  replicate: {
    name: 'Replicate',
    env: 'REPLICATE_API_KEYS',
    description: 'Replicate API密钥',
    example: 'r8_xxx,r8_yyy'
  },
  huggingface: {
    name: 'Hugging Face',
    env: 'HUGGINGFACE_API_KEYS',
    description: 'Hugging Face API密钥',
    example: 'hf_xxx,hf_yyy'
  }
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

function setSecret(name, value) {
  try {
    execSync(`echo "${value}" | wrangler secret put ${name}`, { 
      stdio: 'inherit',
      encoding: 'utf8'
    })
    console.log(`✅ ${name} 设置成功`)
    return true
  } catch (error) {
    console.error(`❌ ${name} 设置失败:`, error.message)
    return false
  }
}

async function setupProvider(providerId, config) {
  console.log(`\n=== ${config.name} ===`)
  console.log(`📝 ${config.description}`)
  console.log(`💡 示例格式: ${config.example}`)
  
  const keys = await question(`请输入${config.name}的API密钥 (多个用逗号分隔，留空跳过): `)
  
  if (keys.trim()) {
    const keyCount = keys.split(',').filter(k => k.trim()).length
    console.log(`🔑 检测到 ${keyCount} 个密钥`)
    
    const confirm = await question(`确认设置这些密钥吗? (y/N): `)
    if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
      return setSecret(config.env, keys.trim())
    } else {
      console.log('⏭️  跳过设置')
      return false
    }
  } else {
    console.log('⏭️  跳过设置')
    return false
  }
}

async function listSecrets() {
  console.log('\n📋 当前已设置的密钥:')
  try {
    const output = execSync('wrangler secret list', { encoding: 'utf8' })
    console.log(output)
  } catch (error) {
    console.error('❌ 无法获取密钥列表:', error.message)
  }
}

async function deleteSecret() {
  console.log('\n🗑️  删除密钥')
  await listSecrets()
  
  const secretName = await question('请输入要删除的密钥名称: ')
  if (secretName.trim()) {
    const confirm = await question(`确认删除 ${secretName} 吗? (y/N): `)
    if (confirm.toLowerCase() === 'y') {
      try {
        execSync(`wrangler secret delete ${secretName}`, { stdio: 'inherit' })
        console.log(`✅ ${secretName} 删除成功`)
      } catch (error) {
        console.error(`❌ 删除失败:`, error.message)
      }
    }
  }
}

async function main() {
  console.log('🤖 ChatAI 密钥管理工具')
  console.log('支持多密钥轮换，提高服务稳定性\n')
  
  while (true) {
    console.log('\n请选择操作:')
    console.log('1. 设置AI平台密钥')
    console.log('2. 查看已设置的密钥')
    console.log('3. 删除密钥')
    console.log('4. 退出')
    
    const choice = await question('\n请输入选项 (1-4): ')
    
    switch (choice) {
      case '1':
        console.log('\n🔧 设置AI平台密钥')
        console.log('建议优先配置: Gemini (免费) > 国内平台 > 国外付费平台\n')
        
        for (const [providerId, config] of Object.entries(AI_PROVIDERS)) {
          await setupProvider(providerId, config)
        }
        
        console.log('\n🎉 密钥设置完成!')
        console.log('💡 系统会根据内容类型和可用性自动选择最佳的AI平台')
        break
        
      case '2':
        await listSecrets()
        break
        
      case '3':
        await deleteSecret()
        break
        
      case '4':
        console.log('👋 再见!')
        rl.close()
        return
        
      default:
        console.log('❌ 无效选项，请重新选择')
    }
  }
}

// 检查wrangler是否可用
try {
  execSync('wrangler --version', { stdio: 'ignore' })
} catch (error) {
  console.error('❌ 未找到 wrangler 命令，请先安装 Cloudflare Wrangler CLI')
  console.error('安装命令: npm install -g wrangler')
  process.exit(1)
}

main().catch(console.error)
