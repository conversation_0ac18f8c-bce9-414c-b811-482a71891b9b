/**
 * 控制台日志清理器
 * 过滤开发和第三方库产生的噪音日志，保持生产环境控制台整洁
 */
(function() {
  'use strict';

  // 检查是否为生产环境，只在生产环境启用日志过滤
  const isProduction = window?.location?.hostname !== 'localhost' &&
                      window?.location?.hostname !== '127.0.0.1' &&
                      !window?.location?.hostname?.includes('localhost');

  // 在开发环境中，可以通过URL参数强制启用过滤器
  const urlParams = new URLSearchParams(window.location.search);
  const forceFilter = urlParams.get('console-filter') === 'true';

  // 如果不是生产环境且没有强制启用，不启用过滤
  if (!isProduction && !forceFilter) {
    console.log('🧹 控制台过滤器: 开发环境，已禁用 (添加 ?console-filter=true 强制启用)');
    return;
  }

  // 保存原始的控制台方法
  const originalLog = console.log;
  const originalInfo = console.info;
  const originalWarn = console.warn;
  const originalError = console.error;

  // 需要过滤的日志关键词列表
  const filterPatterns = [
    // Live2D 相关
    'Live2D',
    '2.1.00_1',
    'profile : Desktop',
    '[PROFILE_NAME]',
    '[USE_ADJUST_TRANSLATION]',
    '[USE_CACHED_POLYGON_IMAGE]',
    '[EXPAND_W]',

    // React 开发工具和错误
    'Download the React DevTools',
    '[HMR] connected',
    '[Fast Refresh] rebuilding',
    'React DevTools',
    'Minified React error',
    'Hydration failed',
    'Text content does not match server-rendered HTML',
    'reactjs.org/docs/error-decoder',

    // Vercel Analytics
    '[Vercel Web Analytics]',
    'Vercel Analytics',

    // 音乐播放器
    'APlayer v1.10.1',
    'http://aplayer.js.org',
    'aplayer',

    // 调试信息
    'Debug mode is enabled',
    '[pageview]',
    '[DEBUG]',

    // 缓存和操作日志
    '执行操作',
    '尝试',
    '成功完成',
    '缓存设置',
    '缓存命中',
    '统计数据更新',
    '使用Umami缓存数据',
    '使用Firebase缓存数据',
    '使用Vercel缓存数据',
    '缓存过期',

    // 资源加载相关
    '✅ 本地资源加载成功',
    '✅ CDN备用资源加载成功',
    '本地资源加载成功',
    'CDN备用资源加载成功',
    'Load Timeout',
    'Load Error',
    'Timeout loading',
    'Failed to load',

    // 第三方库常见日志
    'Prism',
    'prismjs',
    'mermaid',
    'katex',
    'giscus',
    'gitalk',
    'valine',
    'twikoo',
    'artalk',

    // 网络请求相关
    'Failed to load resource',
    'net::ERR_',
    'CORS',

    // 浏览器干预信息
    '[Intervention]',
    'Images loaded lazily',
    'Load events are deferred',
    'go.microsoft.com',

    // Edge Copilot 提示
    '使用 Edge 中的 Copilot',
    '单击',
    '以说明错误',
    '了解更多信息',
    '不再显示',

    // 其他常见噪音
    'Warning:',
    'deprecated',
    'console.time',
    'console.timeEnd'
  ];

  // 检查消息是否应该被过滤
  function shouldFilter(message) {
    const messageStr = String(message).toLowerCase();
    return filterPatterns.some(pattern =>
      messageStr.includes(pattern.toLowerCase())
    );
  }

  // 过滤函数
  function filterConsoleMethod(originalMethod, methodName) {
    return function(...args) {
      // 将所有参数转换为字符串进行检查
      const message = args.join(' ');

      // 如果消息应该被过滤，直接返回
      if (shouldFilter(message)) {
        return;
      }

      // 否则正常输出
      originalMethod.apply(console, args);
    };
  }



  // 对于 error，我们保持更宽松的过滤策略，只过滤明确的噪音
  console.error = function(...args) {
    const message = args.join(' ');

    // 只过滤明确的非关键错误
    const errorFilterPatterns = [
      'Live2D',
      'APlayer',
      'Failed to load resource: the server responded with a status of 404',
      'net::ERR_BLOCKED_BY_CLIENT',
      'Minified React error #425',
      'Minified React error #418',
      'Minified React error #423',
      'Hydration failed because the server rendered',
      'Text content does not match server-rendered HTML',
      'Timeout loading /js/',
      'Failed to load /js/'
    ];

    const shouldFilterError = errorFilterPatterns.some(pattern =>
      message.toLowerCase().includes(pattern.toLowerCase())
    );

    if (!shouldFilterError) {
      originalError.apply(console, args);
    }
  };

  // 添加恢复原始控制台的方法（用于调试）
  window.restoreConsole = function() {
    console.log = originalLog;
    console.info = originalInfo;
    console.warn = originalWarn;
    console.error = originalError;
    originalLog('控制台已恢复原始状态');
  };

  // 添加显示过滤统计的方法
  let filteredCount = 0;

  function filterConsoleMethodWithCount(originalMethod, methodName) {
    return function(...args) {
      const message = args.join(' ');

      if (shouldFilter(message)) {
        filteredCount++;
        return;
      }

      originalMethod.apply(console, args);
    };
  }

  // 重写控制台方法（使用带计数的版本）
  console.log = filterConsoleMethodWithCount(originalLog, 'log');
  console.info = filterConsoleMethodWithCount(originalInfo, 'info');
  console.warn = filterConsoleMethodWithCount(originalWarn, 'warn');

  // 提供查看过滤统计的方法
  window.getFilteredLogCount = function() {
    return filteredCount;
  };

  // 在控制台输出过滤器启用信息
  setTimeout(() => {
    if (isProduction) {
      originalLog('🧹 控制台日志过滤器已启用 - 生产环境模式');
    } else if (forceFilter) {
      originalLog('🧹 控制台日志过滤器已启用 - 强制模式');
    }
  }, 1000);

})();
