/* eslint-disable no-undef */
import { siteConfig } from '@/lib/config'
import { useGlobal } from '@/lib/global'
import { isMobile, loadExternalResource } from '@/lib/utils'
import { useEffect, useRef } from 'react'

export default function Live2D() {
  const { theme } = useGlobal()
  const showPet = JSON.parse(siteConfig('WIDGET_PET'))
  const petLink = siteConfig('WIDGET_PET_LINK')

  const audioRef = useRef(null)
  const lastPlayedIndexRef = useRef(-1)
  const petLoadedRef = useRef(false)
  const scriptLoadedRef = useRef(false)

  const audioUrls = [
    '/live2d/sound/tap_Rhand.mp3',
    '/live2d/sound/tap_body.mp3',
    '/live2d/sound/tap_Lhand.mp3',
    '/live2d/sound/start.mp3'
  ]

  useEffect(() => {
    if (showPet && !isMobile() && !petLoadedRef.current) {
      const initLive2D = () => {
        try {
          if (typeof window?.loadlive2d !== 'undefined') {
            loadlive2d('live2d', petLink)
            petLoadedRef.current = true

            // 延迟添加点击事件，确保模型完全加载
            setTimeout(() => {
              const live2dCanvas = document.getElementById('live2d')
              if (live2dCanvas) {
                // 完全禁用Live2D的内置点击检测功能
                // 重写Live2D的关键函数来阻止点击检测
                if (window.Live2D) {
                  // 禁用所有点击检测相关的函数
                  window.Live2D.hitTest = function() { return null; }
                  window.Live2D.hitTestSimple = function() { return null; }
                  window.Live2D.hitTestCustom = function() { return null; }
                  window.Live2D.hitTestSimpleCustom = function() { return null; }
                  window.Live2D.tapEvent = function() { return; }
                }

                // 重写canvas的事件处理
                const originalAddEventListener = live2dCanvas.addEventListener
                live2dCanvas.addEventListener = function(type, listener, options) {
                  // 阻止Live2D添加任何事件监听器
                  if (listener && listener.toString().includes('hitTest')) {
                    return; // 不添加Live2D的点击事件
                  }
                  return originalAddEventListener.call(this, type, listener, options)
                }

                const safeClickHandler = function safeClickHandler(event) {
                  try {
                    // 完全阻止事件传播
                    event.stopImmediatePropagation()
                    event.preventDefault()
                    playRandomAudio()
                  } catch (e) {
                    // 静默处理音效播放错误
                  }
                }

                // 添加我们自己的点击事件
                live2dCanvas.addEventListener('click', safeClickHandler, true)
              }
            }, 3000) // 延迟3秒，确保模型完全加载完成
          }
        } catch (err) {
          // 静默处理Live2D初始化错误
        }
      }

      if (typeof window?.loadlive2d !== 'undefined') {
        initLive2D()
      } else {
        if (!scriptLoadedRef.current) {
          scriptLoadedRef.current = true

          // 添加全局错误处理器，捕获Live2D的错误
          const originalError = window.onerror
          window.onerror = function(message, source, lineno, colno, error) {
            if (source && source.includes('live2d.min.js')) {
              return true // 静默处理Live2D错误，阻止错误冒泡
            }
            if (originalError) {
              return originalError.call(this, message, source, lineno, colno, error)
            }
            return false
          }

          // 添加未捕获的Promise错误处理
          window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.stack && event.reason.stack.includes('live2d.min.js')) {
              event.preventDefault()
              return true
            }
          })

          loadExternalResource('/js/live2d.min.js', 'js')
            .then(() => {
              // 在Live2D初始化前，先重写所有可能的错误函数
              if (window.Live2D) {
                const originalFunctions = {}

                // 保存并重写所有可能导致错误的函数
                const functionsToOverride = [
                  'hitTest', 'hitTestSimple', 'hitTestCustom', 'hitTestSimpleCustom',
                  'tapEvent', 'mouseEvent', 'touchEvent'
                ]

                functionsToOverride.forEach(funcName => {
                  if (window.Live2D[funcName]) {
                    originalFunctions[funcName] = window.Live2D[funcName]
                    window.Live2D[funcName] = function() {
                      try {
                        return originalFunctions[funcName].apply(this, arguments)
                      } catch (e) {
                        // 静默处理所有Live2D错误
                        return null
                      }
                    }
                  }
                })
              }

              initLive2D()
            })
            .catch(() => {
              // 静默处理脚本加载错误
            })
        }
      }
    }

    return () => {
      const live2dCanvas = document.getElementById('live2d')
      if (live2dCanvas) {
        live2dCanvas.replaceWith(live2dCanvas.cloneNode(true))
      }
    }
  }, [theme, petLink, showPet])

  function playRandomAudio() {
    if (!audioRef.current) {
      audioRef.current = new Audio()
      audioRef.current.preload = 'auto'
    }

    if (!audioRef.current.paused) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }

    let randomIndex
    if (audioUrls.length > 1) {
      do {
        randomIndex = Math.floor(Math.random() * audioUrls.length)
      } while (randomIndex === lastPlayedIndexRef.current)
    } else {
      randomIndex = 0
    }

    // 添加音频加载错误处理
    const tryPlayAudio = (url) => {
      audioRef.current.src = url
      audioRef.current.volume = 1.0
      lastPlayedIndexRef.current = randomIndex

      audioRef.current.onerror = () => {
        // 静默处理音频加载失败
        const backupUrl = url.replace('cdn.jsdelivr.net', 'gcore.jsdelivr.net')
        if (backupUrl !== url) {
          audioRef.current.src = backupUrl
        }
      }

      audioRef.current.play().catch(() => {
        // 静默处理自动播放被阻止
      })
    }

    tryPlayAudio(audioUrls[randomIndex])
  }

  if (!showPet) return null

  return (
    <>
      <canvas
        id='live2d'
        width='280'
        height='250'
        className='cursor-grab pointer-events-auto'
        onMouseDown={e => e.target.classList.add('cursor-grabbing')}
        onMouseUp={e => e.target.classList.remove('cursor-grabbing')}
      />
      <audio ref={audioRef} preload='auto' style={{ display: 'none' }} />
    </>
  )
}
