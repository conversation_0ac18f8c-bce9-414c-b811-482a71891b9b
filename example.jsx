/**
 * FloChatAI 使用示例
 * 展示如何在React项目中集成FloChatAI组件
 */

import React from 'react'
import ChatAIWidget from './components/ChatAI/ChatAIWidget'

function App() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 你的应用内容 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              我的应用
            </h1>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <p className="text-gray-500 text-lg">
              你的应用内容在这里...
            </p>
          </div>
        </div>
      </main>

      {/* FloChatAI 悬浮聊天组件 */}
      <ChatAIWidget
        config={{
          name: 'FloChatAI',
          avatar: 'https://imgbed.wobshare.us.kg/file/1754583573155_wobchatai.png',
          welcomeMessage: '✨ Hello, what can I do for you?',
          position: 'bottom-right', // 或 'bottom-left'
          workerUrl: 'https://your-workers-url.workers.dev' // 替换为你的Workers URL
        }}
      />
    </div>
  )
}

export default App
