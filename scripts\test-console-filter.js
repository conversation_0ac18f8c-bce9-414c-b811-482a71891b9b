/**
 * 控制台过滤器测试脚本
 * 用于验证控制台日志过滤功能是否正常工作
 */

// 模拟浏览器环境
global.window = {
  location: {
    hostname: 'wobshare-4wajpqb27-wob-21s-projects.vercel.app', // 模拟生产环境
    search: ''
  }
};

// 保存原始控制台方法
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error
};

// 测试日志列表
const testLogs = [
  // 应该被过滤的日志
  { type: 'log', message: 'Live2D 2.1.00_1', shouldFilter: true },
  { type: 'log', message: 'profile : Desktop', shouldFilter: true },
  { type: 'log', message: '[PROFILE_NAME] = Desktop', shouldFilter: true },
  { type: 'log', message: 'APlayer v1.10.1 af84efb http://aplayer.js.org', shouldFilter: true },
  { type: 'log', message: '执行操作 fetch_umami_stats, 尝试 1/4', shouldFilter: true },
  { type: 'log', message: '操作 fetch_umami_stats 成功完成', shouldFilter: true },
  { type: 'log', message: '缓存设置: umami_stats (umami, TTL: 120000ms)', shouldFilter: true },
  { type: 'log', message: '✅ 本地资源加载成功: /css/aplayer.min.css', shouldFilter: true },
  { type: 'log', message: '统计数据更新: Object', shouldFilter: true },
  { type: 'log', message: '[Intervention]Images loaded lazily and replaced with placeholders', shouldFilter: true },
  { type: 'log', message: '使用 Edge 中的 Copilot 来解释控制台错误', shouldFilter: true },
  
  // 不应该被过滤的日志
  { type: 'log', message: '用户登录成功', shouldFilter: false },
  { type: 'error', message: '网络请求失败', shouldFilter: false },
  { type: 'warn', message: '表单验证警告', shouldFilter: false },
  { type: 'log', message: '页面加载完成', shouldFilter: false },
  { type: 'info', message: '数据保存成功', shouldFilter: false }
];

/**
 * 测试控制台过滤器
 */
function testConsoleFilter() {
  console.log('🧪 开始测试控制台过滤器');
  console.log('=' * 50);
  
  // 加载控制台清理器代码
  try {
    const fs = require('fs');
    const path = require('path');
    const cleanerCode = fs.readFileSync(
      path.join(__dirname, '../public/js/console-cleaner.js'), 
      'utf8'
    );
    
    // 移除IIFE包装，直接执行
    const executableCode = cleanerCode
      .replace(/^\(function\(\) \{/, '')
      .replace(/\}\)\(\);?$/, '');
    
    eval(executableCode);
    
    console.log('✅ 控制台清理器加载成功');
  } catch (error) {
    console.error('❌ 控制台清理器加载失败:', error.message);
    return;
  }
  
  console.log('\n🔍 测试日志过滤效果:');
  console.log('-' * 30);
  
  let filteredCount = 0;
  let passedCount = 0;
  let correctCount = 0;
  
  // 创建测试用的控制台方法
  const testResults = [];
  
  // 重写控制台方法来捕获输出
  ['log', 'info', 'warn', 'error'].forEach(method => {
    const originalMethod = console[method];
    console[method] = function(...args) {
      const message = args.join(' ');
      testResults.push({ method, message, filtered: false });
      // 不实际输出，只记录
    };
  });
  
  // 测试每个日志
  testLogs.forEach((testLog, index) => {
    const beforeCount = testResults.length;
    
    // 执行日志输出
    console[testLog.type](testLog.message);
    
    const afterCount = testResults.length;
    const wasFiltered = afterCount === beforeCount;
    
    // 检查结果是否符合预期
    const isCorrect = wasFiltered === testLog.shouldFilter;
    
    if (wasFiltered) {
      filteredCount++;
    } else {
      passedCount++;
    }
    
    if (isCorrect) {
      correctCount++;
    }
    
    const status = isCorrect ? '✅' : '❌';
    const filterStatus = wasFiltered ? '🚫 已过滤' : '✅ 已通过';
    const expectStatus = testLog.shouldFilter ? '应过滤' : '应通过';
    
    console.log(`${status} 测试${index + 1}: ${filterStatus} (${expectStatus})`);
    console.log(`   消息: "${testLog.message.substring(0, 50)}${testLog.message.length > 50 ? '...' : ''}"`);
  });
  
  // 恢复原始控制台方法
  Object.assign(console, originalConsole);
  
  // 输出测试结果
  console.log('\n📊 测试结果统计:');
  console.log(`总测试数: ${testLogs.length}`);
  console.log(`正确处理: ${correctCount}/${testLogs.length} (${(correctCount/testLogs.length*100).toFixed(1)}%)`);
  console.log(`已过滤: ${filteredCount}`);
  console.log(`已通过: ${passedCount}`);
  
  if (correctCount === testLogs.length) {
    console.log('\n🎉 所有测试通过！控制台过滤器工作正常');
  } else {
    console.log('\n⚠️  部分测试失败，需要调整过滤规则');
  }
  
  // 输出建议
  console.log('\n💡 使用建议:');
  console.log('• 生产环境: 自动启用过滤');
  console.log('• 开发环境: 添加 ?console-filter=true 启用');
  console.log('• 调试时: 调用 window.restoreConsole() 恢复');
  console.log('• 统计: 调用 window.getFilteredLogCount() 查看过滤数量');
}

// 运行测试
if (require.main === module) {
  testConsoleFilter();
}

module.exports = { testConsoleFilter };
