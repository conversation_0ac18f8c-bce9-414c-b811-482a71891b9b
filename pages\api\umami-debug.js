/**
 * Umami API 调试端点
 * 用于测试不同的 API 调用方式
 */

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const websiteId = process.env.UMAMI_WEBSITE_ID || '8e485e40-2d2b-44b2-b792-7d00bbc235dd'
  const apiKey = process.env.UMAMI_API_KEY || 'api_J5rJQDJgh3a4a57ni3uKCZbgHFCAD6s6'

  const debugInfo = {
    environment: {
      hasWebsiteId: !!process.env.UMAMI_WEBSITE_ID,
      hasApiKey: !!process.env.UMAMI_API_KEY,
      websiteId: websiteId,
      apiKeyPrefix: apiKey.substring(0, 10) + '...',
      nodeEnv: process.env.NODE_ENV
    },
    tests: []
  }

  // 测试 1: 基本连接测试（使用正确的 API 端点）
  try {
    console.log('测试 1: 基本连接到 Umami Cloud v1 API')
    const response = await fetch('https://api.umami.is/v1/me', {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json'
      }
    })

    debugInfo.tests.push({
      name: 'Basic Connection (v1 API)',
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text()
    })
  } catch (error) {
    debugInfo.tests.push({
      name: 'Basic Connection (v1 API)',
      success: false,
      error: error.message
    })
  }

  // 测试 2: 网站信息获取
  try {
    console.log('测试 2: 获取网站信息')
    const response = await fetch(`https://api.umami.is/v1/websites/${websiteId}`, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    debugInfo.tests.push({
      name: 'Website Info (v1)',
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text()
    })
  } catch (error) {
    debugInfo.tests.push({
      name: 'Website Info (v1)',
      success: false,
      error: error.message
    })
  }

  // 测试 3: 统计数据获取（简化版）
  try {
    console.log('测试 3: 获取统计数据')
    const response = await fetch(`https://api.umami.is/v1/websites/${websiteId}/stats`, {
      method: 'GET',
      headers: {
        'x-umami-api-key': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    debugInfo.tests.push({
      name: 'Stats (No Params) v1',
      status: response.status,
      success: response.ok,
      response: response.ok ? await response.json() : await response.text()
    })
  } catch (error) {
    debugInfo.tests.push({
      name: 'Stats (No Params) v1',
      success: false,
      error: error.message
    })
  }

  // 测试 4: 不同的认证头格式（使用 v1 API）
  const authMethods = [
    { name: 'X-Umami-API-Key (Correct)', headers: { 'x-umami-api-key': apiKey } },
    { name: 'Bearer Token (Legacy)', headers: { 'Authorization': `Bearer ${apiKey}` } },
    { name: 'Direct Token', headers: { 'Authorization': apiKey } },
    { name: 'X-API-Key', headers: { 'x-api-key': apiKey } }
  ]

  for (const method of authMethods) {
    try {
      console.log(`测试认证方法: ${method.name}`)
      const response = await fetch(`https://api.umami.is/v1/websites/${websiteId}`, {
        method: 'GET',
        headers: {
          ...method.headers,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      debugInfo.tests.push({
        name: `Auth: ${method.name}`,
        status: response.status,
        success: response.ok,
        response: response.ok ? 'Success' : await response.text()
      })
    } catch (error) {
      debugInfo.tests.push({
        name: `Auth: ${method.name}`,
        success: false,
        error: error.message
      })
    }
  }

  res.status(200).json(debugInfo)
}
